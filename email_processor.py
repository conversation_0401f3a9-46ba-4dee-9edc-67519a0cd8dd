"""
Email Batch Processor - A PyQt6 application for processing .eml files
Extracts comprehensive information from email files and exports to Excel
"""

import sys
import os
import email
import email.header
import email.utils
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import chardet
from datetime import datetime
from pathlib import Path
import traceback
import tempfile
import time
import io
import zipfile
import tarfile
import gzip
import re
import html
import codecs
import base64
import mimetypes
import threading
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QTextEdit, QProgressBar,
                            QFileDialog, QMessageBox, QGroupBox, QGridLayout,
                            QLineEdit, QCheckBox, QSplitter, QFrame, QSpinBox)
from PyQt6.QtCore import QThread, pyqt<PERSON><PERSON><PERSON>, <PERSON>t, <PERSON><PERSON><PERSON>, QMutex, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap

# Import modern styling
from modern_styles import get_modern_stylesheet, apply_button_class, apply_label_class, COLORS

import openpyxl
from openpyxl.styles import Font, Alignment

# Import attachment processing libraries (with fallbacks)
try:
    import rarfile
    RARFILE_AVAILABLE = True
except ImportError:
    RARFILE_AVAILABLE = False

try:
    import py7zr
    PY7ZR_AVAILABLE = True
except ImportError:
    PY7ZR_AVAILABLE = False

# PaddleOCR service
try:
    from paddle_ocr_service import PaddleOcrService
    OCR_SERVICE_AVAILABLE = True
except Exception:
    OCR_SERVICE_AVAILABLE = False

# Suppress openpyxl warnings
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

# Configure logging for threading operations
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_processor_threading.log', mode='a')
    ]
)
thread_logger = logging.getLogger('EmailProcessor.Threading')

try:
    from docx import Document
    from pptx import Presentation
    import xlrd
    OFFICE_AVAILABLE = True
except ImportError:
    OFFICE_AVAILABLE = False


class ThreadingConfiguration:
    """Configuration and utilities for multi-threading support"""

    @staticmethod
    def get_optimal_thread_count():
        """Get the optimal number of worker threads based on system resources"""
        cpu_count = os.cpu_count() or 1
        # For OCR-heavy workloads, limit to 4 threads max to avoid memory issues
        # Each PaddleOCR instance uses ~100MB of memory
        return min(4, max(1, cpu_count))

    @staticmethod
    def calculate_memory_usage(thread_count):
        """Calculate estimated memory usage with system-specific adjustments"""
        try:
            import psutil
            available_memory = psutil.virtual_memory().available / (1024**3)  # GB
            total_memory = psutil.virtual_memory().total / (1024**3)  # GB

            # Base memory per thread from actual measurements
            base_memory = 1.117  # GB per thread

            # Adjust based on system memory availability
            if total_memory < 8:
                # Low memory systems - reduce estimate
                base_memory = min(0.8, available_memory / (thread_count * 2))
            elif total_memory < 16:
                # Medium memory systems - moderate estimate
                base_memory = min(1.0, available_memory / (thread_count * 1.5))
            else:
                # High memory systems - use full estimate but cap at available
                base_memory = min(1.117, available_memory / (thread_count * 1.2))

            return thread_count * base_memory

        except ImportError:
            # Fallback if psutil is not available
            base_memory = 1.117  # Original estimate
            return thread_count * base_memory
        except Exception as e:
            print(f"Warning: Memory calculation failed: {e}")
            # Conservative fallback
            return thread_count * 0.5

    @staticmethod
    def get_dynamic_thread_count(file_count, system_load=None):
        """Dynamic thread count based on workload and system resources"""
        try:
            import psutil
            cpu_count = os.cpu_count() or 1
            memory_gb = psutil.virtual_memory().total / (1024**3)
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            cpu_usage = psutil.cpu_percent(interval=0.1) if system_load is None else system_load

            # Base thread count limits based on available memory
            if memory_gb < 8:
                max_threads = 2
            elif memory_gb < 16:
                max_threads = 3
            else:
                max_threads = 4

            # Adjust based on available memory (need ~1GB per thread)
            memory_limited_threads = max(1, int(available_memory_gb / 1.5))
            max_threads = min(max_threads, memory_limited_threads)

            # Adjust based on CPU usage
            if cpu_usage > 80:
                max_threads = max(1, max_threads - 1)

            # Scale based on file count
            if file_count < 10:
                optimal_threads = min(2, max_threads)
            elif file_count < 50:
                optimal_threads = min(3, max_threads)
            else:
                optimal_threads = max_threads

            thread_logger.info(f"Dynamic thread calculation: files={file_count}, memory={memory_gb:.1f}GB, "
                             f"available={available_memory_gb:.1f}GB, cpu_usage={cpu_usage:.1f}%, "
                             f"optimal_threads={optimal_threads}")

            return optimal_threads

        except ImportError:
            thread_logger.warning("psutil not available, using static thread count")
            return ThreadingConfiguration.get_optimal_thread_count()
        except Exception as e:
            thread_logger.error(f"Dynamic thread calculation failed: {e}")
            return ThreadingConfiguration.get_optimal_thread_count()

    @staticmethod
    def is_threading_beneficial(file_count):
        """Determine if threading would be beneficial for given file count"""
        return file_count >= 5

    @staticmethod
    def should_use_threading(file_count, estimated_memory_per_thread=1.1):
        """Intelligent threading decision based on system resources"""
        try:
            import psutil
            available_memory = psutil.virtual_memory().available / (1024**3)
            total_memory = psutil.virtual_memory().total / (1024**3)
            cpu_count = psutil.cpu_count()

            # Basic requirements
            if file_count < 5 or cpu_count < 2:
                return False

            # Memory requirements (need at least 2 threads worth of memory)
            required_memory = estimated_memory_per_thread * 2
            if available_memory < required_memory:
                thread_logger.warning(f"Insufficient memory for threading: available={available_memory:.1f}GB, "
                                    f"required={required_memory:.1f}GB")
                return False

            # System load check
            try:
                cpu_usage = psutil.cpu_percent(interval=0.1)
                if cpu_usage > 90:
                    thread_logger.warning(f"High CPU usage detected: {cpu_usage:.1f}%, skipping threading")
                    return False
            except Exception:
                pass  # Continue if CPU usage check fails

            thread_logger.info(f"Threading approved: files={file_count}, memory={available_memory:.1f}GB available, "
                             f"total_memory={total_memory:.1f}GB")
            return True

        except ImportError:
            thread_logger.warning("psutil not available, using basic file count check")
            return file_count >= 5
        except Exception as e:
            thread_logger.error(f"Threading decision failed: {e}")
            return file_count >= 5


class ThreadLocalOCR:
    """Thread-local OCR instance manager for safe multi-threading"""

    def __init__(self):
        self._local = threading.local()

    def get_ocr_service(self):
        """Get thread-local OCR service instance"""
        if not hasattr(self._local, 'ocr_service'):
            if OCR_SERVICE_AVAILABLE:
                from paddle_ocr_service import PaddleOcrService
                self._local.ocr_service = PaddleOcrService()
            else:
                self._local.ocr_service = None
        return self._local.ocr_service

    def cleanup_thread_local(self):
        """Clean up thread-local OCR resources - should be called from worker threads"""
        try:
            if hasattr(self._local, 'ocr_service') and self._local.ocr_service is not None:
                # Properly close OCR service if it has cleanup methods
                ocr_service = self._local.ocr_service
                if hasattr(ocr_service, 'close'):
                    try:
                        ocr_service.close()
                    except Exception as close_error:
                        print(f"Warning: OCR service close failed: {close_error}")

                # Clear the thread-local reference
                del self._local.ocr_service
                print("Thread-local OCR resources cleaned up successfully")
        except Exception as e:
            # Log the error instead of silently ignoring
            print(f"Warning: OCR cleanup failed: {e}")
            import traceback
            traceback.print_exc()


# Global thread-local OCR manager
thread_local_ocr = ThreadLocalOCR()


class EmailParser:
    """Class to handle email parsing and data extraction"""

    def __init__(self):
        self.supported_encodings = ['utf-8', 'gb2312', 'gbk', 'big5', 'iso-8859-1']

        # Initialize OCR service abstraction
        self.ocr_engine = None  # kept for backward compatibility (unused)
        self.ocr_status = "unavailable"
        try:
            if OCR_SERVICE_AVAILABLE:
                self.ocr_service = PaddleOcrService()
                self.ocr_status = self.ocr_service.status

                # If PaddleOCR has issues, gracefully degrade
                if not self.ocr_service.is_available():
                    self.ocr_service = None
                    self.ocr_status = "unavailable:paddleocr_issues"
            else:
                self.ocr_service = None
        except Exception as _e:
            # If service fails to initialize, keep unavailable status
            self.ocr_service = None
            self.ocr_status = f"unavailable: {str(_e)[:80]}"

        # Archive file extensions
        self.archive_extensions = {'.zip', '.rar', '.7z', '.tar', '.gz', '.tar.gz', '.tgz'}

        # Image file extensions for OCR
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif'}

        # Document file extensions
        self.office_extensions = {'.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'}

    def sanitize_excel_value(self, value):
        """Sanitize value for safe Excel export to prevent corruption and formula injection"""
        if value is None:
            return ""

        # Convert to string and handle Unicode properly
        try:
            if isinstance(value, bytes):
                # Try to decode bytes with proper encoding
                str_value = value.decode('utf-8', errors='replace')
            else:
                str_value = str(value)
        except Exception:
            str_value = str(value)

        # Remove XML-invalid characters that can corrupt Excel files
        # XML 1.0 valid characters: #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]
        def is_xml_valid_char(char):
            """Check if character is valid in XML 1.0"""
            code = ord(char)
            return (
                code == 0x09 or  # Tab
                code == 0x0A or  # Line feed
                code == 0x0D or  # Carriage return
                (0x20 <= code <= 0xD7FF) or  # Basic multilingual plane
                (0xE000 <= code <= 0xFFFD) or  # Private use area
                (0x10000 <= code <= 0x10FFFF)  # Supplementary planes
            )

        # Filter out XML-invalid characters
        str_value = ''.join(char for char in str_value if is_xml_valid_char(char))

        # Additional cleanup for problematic characters that can cause Excel issues
        # Remove or replace characters that cause Excel XML corruption
        problematic_replacements = {
            '\x0B': ' ',  # Vertical tab -> space
            '\x0C': ' ',  # Form feed -> space
            '\x1C': ' ',  # File separator -> space
            '\x1D': ' ',  # Group separator -> space
            '\x1E': ' ',  # Record separator -> space
            '\x1F': ' ',  # Unit separator -> space
        }

        for old_char, new_char in problematic_replacements.items():
            str_value = str_value.replace(old_char, new_char)

        # Handle Unicode normalization to prevent encoding issues
        try:
            import unicodedata
            str_value = unicodedata.normalize('NFC', str_value)
        except Exception:
            pass  # If normalization fails, continue with original string

        # Prevent formula injection by prefixing with single quote if starts with formula characters
        formula_chars = ['=', '+', '-', '@']
        if str_value and str_value[0] in formula_chars:
            str_value = "'" + str_value

        # Clean up excessive whitespace that can cause formatting issues
        str_value = re.sub(r'\s+', ' ', str_value).strip()

        # Truncate if too long for Excel cell (32,767 character limit)
        if len(str_value) > 32767:
            str_value = str_value[:32764] + "..."

        return str_value
    
    def detect_encoding(self, raw_data):
        """Detect the encoding of raw email data"""
        try:
            detected = chardet.detect(raw_data)
            if detected['encoding']:
                return detected['encoding']
        except:
            pass
        
        # Try common encodings for Chinese emails
        for encoding in self.supported_encodings:
            try:
                raw_data.decode(encoding)
                return encoding
            except:
                continue
        
        return 'utf-8'  # fallback
    
    def decode_header(self, header_value):
        """Decode email header that might be encoded"""
        if not header_value:
            return ""
        
        try:
            decoded_parts = email.header.decode_header(header_value)
            decoded_string = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            decoded_string += part.decode(encoding)
                        except:
                            # Try to detect encoding if specified encoding fails
                            detected_encoding = self.detect_encoding(part)
                            decoded_string += part.decode(detected_encoding, errors='ignore')
                    else:
                        # Try to detect encoding
                        detected_encoding = self.detect_encoding(part)
                        decoded_string += part.decode(detected_encoding, errors='ignore')
                else:
                    decoded_string += str(part)
            
            return decoded_string.strip()
        except Exception:
            return str(header_value)
    
    def extract_email_addresses(self, header_value):
        """Extract email addresses and display names from header"""
        if not header_value:
            return []
        
        try:
            addresses = email.utils.getaddresses([header_value])
            result = []
            for name, addr in addresses:
                decoded_name = self.decode_header(name) if name else ""
                result.append({
                    'name': decoded_name,
                    'email': addr,
                    'full': f"{decoded_name} <{addr}>" if decoded_name else addr
                })
            return result
        except:
            return [{'name': '', 'email': str(header_value), 'full': str(header_value)}]
    
    def get_email_body(self, msg):
        """Extract and clean email body content with HTML cleanup and Unicode processing"""
        body_text = ""
        body_html = ""
        has_html_content = False

        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition", ""))

                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue

                    if content_type == "text/plain":
                        payload = part.get_payload(decode=True)
                        if payload:
                            charset = part.get_content_charset() or 'utf-8'
                            try:
                                body_text += payload.decode(charset, errors='ignore')
                            except:
                                detected_encoding = self.detect_encoding(payload)
                                body_text += payload.decode(detected_encoding, errors='ignore')

                    elif content_type == "text/html":
                        payload = part.get_payload(decode=True)
                        if payload:
                            charset = part.get_content_charset() or 'utf-8'
                            try:
                                html_content = payload.decode(charset, errors='ignore')
                                body_html += html_content
                                has_html_content = True
                            except:
                                detected_encoding = self.detect_encoding(payload)
                                html_content = payload.decode(detected_encoding, errors='ignore')
                                body_html += html_content
                                has_html_content = True
            else:
                # Single part message
                payload = msg.get_payload(decode=True)
                if payload:
                    charset = msg.get_content_charset() or 'utf-8'
                    try:
                        content = payload.decode(charset, errors='ignore')
                        if msg.get_content_type() == "text/html":
                            body_html = content
                            has_html_content = True
                        else:
                            body_text = content
                    except:
                        detected_encoding = self.detect_encoding(payload)
                        content = payload.decode(detected_encoding, errors='ignore')
                        if msg.get_content_type() == "text/html":
                            body_html = content
                            has_html_content = True
                        else:
                            body_text = content

        except Exception as e:
            body_text = f"Error extracting body: {str(e)}"

        # Choose the best content: prefer HTML if it exists and contains substantial content
        # or if plain text is minimal/generic
        final_content = ""
        if body_html and body_text:
            # Both HTML and plain text exist - use intelligent selection
            plain_text_cleaned = body_text.strip().lower()

            # Check if plain text is minimal or generic
            generic_phrases = [
                "plain text version",
                "view this email in your browser",
                "if you cannot view this email",
                "click here to view",
                "this is a html email"
            ]

            is_generic_plain_text = (
                len(plain_text_cleaned) < 50 or  # Very short
                any(phrase in plain_text_cleaned for phrase in generic_phrases)
            )

            if is_generic_plain_text:
                final_content = body_html
            else:
                # Use HTML if it's significantly longer (more content)
                if len(body_html.strip()) > len(body_text.strip()) * 1.5:
                    final_content = body_html
                else:
                    final_content = body_text
        else:
            # Use whichever exists
            final_content = body_html or body_text

        # Store whether we had HTML content for later use
        self._last_had_html = has_html_content

        # Process and clean the content
        if final_content:
            final_content = self._clean_email_content(final_content)

        return final_content

    def _clean_email_content(self, content):
        """Clean email content by removing HTML tags and processing Unicode escape sequences"""
        if not content:
            return content

        try:
            # Step 1: Decode Unicode escape sequences (like \u60a8 -> 您)
            content = self._decode_unicode_escapes(content)

            # Step 2: Clean HTML content if present
            if self._contains_html(content):
                content = self._clean_html_content(content)

            # Step 3: Decode HTML entities (like &nbsp; -> space)
            content = html.unescape(content)

            # Step 4: Clean up excessive whitespace and normalize line breaks
            content = self._normalize_whitespace(content)

            return content

        except Exception as e:
            # If cleaning fails, return original content with error note
            return f"{content}\n[Content cleaning error: {str(e)}]"

    def _decode_unicode_escapes(self, text):
        """Decode Unicode escape sequences like \\u60a8 to actual Unicode characters"""
        if not text or '\\u' not in text:
            return text

        try:
            # Simple and reliable approach: regex replacement
            def decode_match(match):
                try:
                    unicode_value = match.group(1)
                    return chr(int(unicode_value, 16))
                except (ValueError, OverflowError):
                    return match.group(0)  # Return original if can't decode

            # Pattern to match Unicode escape sequences
            unicode_pattern = r'\\u([0-9a-fA-F]{4})'

            # Replace all Unicode escape sequences
            result = re.sub(unicode_pattern, decode_match, text)

            return result

        except Exception:
            return text  # Return original if decoding fails

    def _contains_html(self, content):
        """Check if content contains HTML tags"""
        html_pattern = r'<[^>]+>'
        return bool(re.search(html_pattern, content))

    def _clean_html_content(self, html_content):
        """Remove HTML tags, CSS styles, VML declarations and convert to readable plain text"""
        try:
            # Step 1: Remove CSS style blocks and inline styles
            # Remove <style> blocks completely (including content)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.IGNORECASE | re.DOTALL)

            # Remove CSS style definitions that appear as plain text
            # Match CSS patterns like "body { line-height: 1.5; }"
            html_content = re.sub(r'[a-zA-Z0-9\-_#\.]+\s*\{[^}]*\}', '', html_content, flags=re.MULTILINE)

            # Remove VML behavior declarations
            # Match patterns like "v\:* {behavior:url(#default#VML);}"
            html_content = re.sub(r'[a-zA-Z0-9\\:*]+\s*\{behavior:url\([^)]*\);\}', '', html_content)

            # Remove CSS @import and @media rules
            html_content = re.sub(r'@(import|media)[^;{]*[;{][^}]*}', '', html_content, flags=re.IGNORECASE)

            # Remove inline style attributes from HTML tags
            html_content = re.sub(r'style\s*=\s*["\'][^"\']*["\']', '', html_content, flags=re.IGNORECASE)

            # Remove class attributes that often contain CSS references
            html_content = re.sub(r'class\s*=\s*["\'][^"\']*["\']', '', html_content, flags=re.IGNORECASE)

            # Step 2: Remove script blocks
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.IGNORECASE | re.DOTALL)

            # Step 3: Convert common HTML elements to text equivalents
            # Convert line breaks
            html_content = re.sub(r'<br\s*/?>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<br\s+[^>]*>', '\n', html_content, flags=re.IGNORECASE)

            # Convert paragraph breaks
            html_content = re.sub(r'</p>', '\n\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<p[^>]*>', '', html_content, flags=re.IGNORECASE)

            # Convert div breaks
            html_content = re.sub(r'</div>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<div[^>]*>', '', html_content, flags=re.IGNORECASE)

            # Convert list items
            html_content = re.sub(r'<li[^>]*>', '• ', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</li>', '\n', html_content, flags=re.IGNORECASE)

            # Convert headers to text with emphasis
            html_content = re.sub(r'<h[1-6][^>]*>', '\n=== ', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</h[1-6]>', ' ===\n', html_content, flags=re.IGNORECASE)

            # Convert table elements
            html_content = re.sub(r'</?table[^>]*>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</?tr[^>]*>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</?td[^>]*>', '\t', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</?th[^>]*>', '\t', html_content, flags=re.IGNORECASE)

            # Step 4: Remove all remaining HTML tags
            html_content = re.sub(r'<[^>]+>', '', html_content)

            # Step 5: Clean up CSS-like content that might remain as plain text
            # Remove remaining CSS property declarations
            html_content = re.sub(r'[a-zA-Z\-]+\s*:\s*[^;]+;', '', html_content)

            # Remove font-family declarations that often appear
            html_content = re.sub(r'font-family\s*:\s*[^;]+', '', html_content, flags=re.IGNORECASE)

            # Remove color declarations
            html_content = re.sub(r'color\s*:\s*[^;]+', '', html_content, flags=re.IGNORECASE)

            # Step 6: Clean up the result
            return html_content

        except Exception:
            # If HTML cleaning fails, just remove tags and basic CSS
            cleaned = re.sub(r'<[^>]+>', '', html_content)
            cleaned = re.sub(r'[a-zA-Z0-9\-_#\.]+\s*\{[^}]*\}', '', cleaned)
            return cleaned

    def _normalize_whitespace(self, text):
        """Normalize whitespace and line breaks for better readability"""
        try:
            # Replace multiple spaces with single space
            text = re.sub(r' +', ' ', text)

            # Replace multiple line breaks with maximum of 2
            text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

            # Remove trailing whitespace from lines
            text = re.sub(r'[ \t]+$', '', text, flags=re.MULTILINE)

            # Remove leading/trailing whitespace from entire text
            text = text.strip()

            return text

        except Exception:
            return text.strip()  # Basic cleanup if regex fails
    
    def get_attachments_info(self, msg):
        """Extract comprehensive attachment information with content processing"""
        attachments = []
        processing_start_time = time.time()

        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content_disposition = str(part.get("Content-Disposition", ""))

                    if "attachment" in content_disposition:
                        filename = part.get_filename()
                        if filename:
                            # Decode filename if it's encoded
                            decoded_filename = self.decode_header(filename)

                            # Get file size and data
                            payload = part.get_payload(decode=True)
                            size = len(payload) if payload else 0

                            attachment_info = {
                                'filename': decoded_filename,
                                'size': size,
                                'content_type': part.get_content_type(),
                                'payload': payload,  # Store payload for debugging/processing
                                # New fields for comprehensive processing
                                'archive_file_list': '',
                                'archive_file_count': 0,
                                'archive_total_size': 0,
                                'ocr_extracted_text': '',
                                'document_content': '',
                                'processing_status': 'not_processed',
                                'processing_error': ''
                            }

                            if payload and size > 0:
                                # Process based on file type
                                file_ext = Path(decoded_filename).suffix.lower()

                                # Process archive files
                                if file_ext in self.archive_extensions:
                                    archive_result = self.process_archive_attachment(payload, decoded_filename)
                                    attachment_info['archive_file_list'] = '; '.join(archive_result['file_list'])
                                    attachment_info['archive_file_count'] = archive_result['file_count']
                                    attachment_info['archive_total_size'] = archive_result['total_size']

                                    # Enhanced status information
                                    status = archive_result['status']
                                    if archive_result.get('format_used'):
                                        status += f" ({archive_result['format_used']})"
                                    if archive_result.get('attempts'):
                                        status += f" [Attempts: {'; '.join(archive_result['attempts'])}]"

                                    attachment_info['processing_status'] = status
                                    attachment_info['processing_error'] = archive_result['error']

                                # Process images and PDFs with OCR
                                elif file_ext in self.image_extensions or file_ext == '.pdf':
                                    ocr_result = self.process_ocr_attachment(payload, decoded_filename)
                                    attachment_info['ocr_extracted_text'] = ocr_result['extracted_text']
                                    if attachment_info['processing_status'] == 'not_processed':
                                        attachment_info['processing_status'] = ocr_result['status']
                                        attachment_info['processing_error'] = ocr_result['error']

                                # Process Office documents
                                elif file_ext in self.office_extensions:
                                    doc_result = self.process_document_attachment(payload, decoded_filename)
                                    attachment_info['document_content'] = doc_result['extracted_content']
                                    if attachment_info['processing_status'] == 'not_processed':
                                        attachment_info['processing_status'] = doc_result['status']
                                        attachment_info['processing_error'] = doc_result['error']

                                else:
                                    attachment_info['processing_status'] = 'skipped'
                                    attachment_info['processing_error'] = 'File type not supported for content extraction'

                            attachments.append(attachment_info)

        except Exception as e:
            # Add error information to the last attachment if processing failed
            if attachments:
                attachments[-1]['processing_status'] = 'error'
                attachments[-1]['processing_error'] = f'General processing error: {str(e)}'

        processing_time = time.time() - processing_start_time

        # Add processing time to all attachments
        for attachment in attachments:
            attachment['processing_time'] = processing_time / len(attachments) if attachments else 0

        return attachments

    def process_archive_attachment(self, file_data, filename):
        """Process archive attachments with intelligent format detection and fallback"""
        result = {
            'file_list': [],
            'file_count': 0,
            'total_size': 0,
            'status': 'success',
            'error': '',
            'format_used': '',
            'attempts': []
        }

        try:
            file_ext = Path(filename).suffix.lower()

            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
                temp_file.write(file_data)
                temp_path = temp_file.name

            try:
                # Try intelligent format detection with fallback
                result = self._process_archive_with_fallback(temp_path, file_ext)

            finally:
                # Clean up temporary file with retry
                import time
                for attempt in range(3):
                    try:
                        os.unlink(temp_path)
                        break
                    except (OSError, PermissionError):
                        if attempt < 2:
                            time.sleep(0.1)  # Brief delay before retry
                        # If all attempts fail, the file will be cleaned up by the OS eventually

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            result['attempts'].append(f"General error: {str(e)}")

        return result

    def _fix_filename_encoding(self, filename):
        """Fix encoding issues in filenames, especially for Chinese characters"""
        if not filename:
            return filename

        try:
            # If filename is already a string, check if it needs encoding fix
            if isinstance(filename, str):
                # Check for common garbled character patterns that indicate encoding issues
                garbled_patterns = [
                    '╔╧╜╗╦∙╨¡▓Θ║»',  # Common pattern from CP437/CP850 -> UTF-8 conversion
                    'í▓í│║┼┴¬┤ó╓ñ╚»',  # Another common pattern
                    '╞┌╘┬╚╒',  # Date-related garbled text
                    '▓Γ╩╘╬─╡╡',  # CP437 garbled pattern
                    '²âÊÔÎÄµµ',  # Latin1 garbled pattern
                ]

                # Also check for high concentration of box-drawing and special characters
                special_chars = len([c for c in filename if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚»╞┌╘┬╚╒▓Γ╩╘╬─╡╡²âÊÔÎÄµµ'])
                has_garbled = any(pattern in filename for pattern in garbled_patterns) or (special_chars > 3)

                # Check if filename already contains proper Chinese characters - if so, don't convert
                existing_chinese = len([c for c in filename if '\u4e00' <= c <= '\u9fff'])

                # Only attempt conversion if we detect garbled patterns and no existing Chinese
                if has_garbled and existing_chinese == 0:
                    # Try different encoding combinations, prioritizing CP437 which works best
                    encodings_to_try = [
                        ('cp437', 'gbk'),      # Most successful for the user's case
                        ('cp437', 'gb2312'),   # Alternative Chinese encoding
                        ('cp850', 'gbk'),      # Alternative codepage
                        ('cp850', 'gb2312'),
                        ('latin-1', 'gbk'),
                        ('latin-1', 'gb2312'),
                        ('cp936', 'utf-8'),
                        ('windows-1252', 'gbk'),
                    ]

                    best_result = filename
                    best_score = 0

                    for from_enc, to_enc in encodings_to_try:
                        try:
                            # Try to re-encode and decode
                            fixed_filename = filename.encode(from_enc, errors='ignore').decode(to_enc, errors='ignore')

                            # Score the result based on multiple criteria
                            chinese_chars = len([c for c in fixed_filename if '\u4e00' <= c <= '\u9fff'])
                            garbled_chars = len([c for c in fixed_filename if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚»'])
                            control_chars = len([c for c in fixed_filename if ord(c) < 32])

                            # Calculate improvement score
                            score = chinese_chars * 10 - garbled_chars * 5 - control_chars * 2

                            # Bonus for reasonable length (not too much shrinkage)
                            if len(fixed_filename) >= len(filename) * 0.5:
                                score += 5

                            if score > best_score and fixed_filename != filename:
                                best_result = fixed_filename
                                best_score = score

                        except (UnicodeEncodeError, UnicodeDecodeError, LookupError):
                            continue

                    return best_result

                return filename

            elif isinstance(filename, bytes):
                # If filename is bytes, try to decode with appropriate encoding
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'cp936', 'latin-1', 'cp437']

                for encoding in encodings_to_try:
                    try:
                        decoded = filename.decode(encoding)
                        # Prefer UTF-8 compatible results
                        if encoding in ['utf-8', 'gbk', 'gb2312']:
                            return decoded
                        # For other encodings, check if result contains reasonable characters
                        if all(ord(c) >= 32 or c in '\t\n\r' for c in decoded):
                            return decoded
                    except (UnicodeDecodeError, LookupError):
                        continue

                # If all decodings fail, use error handling
                return filename.decode('utf-8', errors='replace')

        except Exception:
            # If all else fails, return original or string representation
            return str(filename)

        return filename

    def _process_archive_with_fallback(self, temp_path, file_ext):
        """Process archive with intelligent format detection and fallback mechanism"""
        result = {
            'file_list': [],
            'file_count': 0,
            'total_size': 0,
            'status': 'success',
            'error': '',
            'format_used': '',
            'attempts': []
        }

        # Define processing order based on file extension
        if file_ext == '.zip':
            processors = [
                ('ZIP', self._process_zip_file),
                ('RAR', self._process_rar_file) if RARFILE_AVAILABLE else None,
                ('7Z', self._process_7z_file) if PY7ZR_AVAILABLE else None,
                ('TAR', self._process_tar_file)
            ]
        elif file_ext == '.rar':
            processors = [
                ('RAR', self._process_rar_file) if RARFILE_AVAILABLE else None,
                ('ZIP', self._process_zip_file),
                ('7Z', self._process_7z_file) if PY7ZR_AVAILABLE else None,
                ('TAR', self._process_tar_file)
            ]
        elif file_ext == '.7z':
            processors = [
                ('7Z', self._process_7z_file) if PY7ZR_AVAILABLE else None,
                ('ZIP', self._process_zip_file),
                ('RAR', self._process_rar_file) if RARFILE_AVAILABLE else None,
                ('TAR', self._process_tar_file)
            ]
        elif file_ext in ['.tar', '.gz', '.tar.gz', '.tgz']:
            processors = [
                ('TAR', self._process_tar_file),
                ('ZIP', self._process_zip_file),
                ('RAR', self._process_rar_file) if RARFILE_AVAILABLE else None,
                ('7Z', self._process_7z_file) if PY7ZR_AVAILABLE else None
            ]
        else:
            # Unknown extension - try all formats
            processors = [
                ('ZIP', self._process_zip_file),
                ('RAR', self._process_rar_file) if RARFILE_AVAILABLE else None,
                ('7Z', self._process_7z_file) if PY7ZR_AVAILABLE else None,
                ('TAR', self._process_tar_file)
            ]

        # Filter out None processors (unavailable libraries)
        processors = [p for p in processors if p is not None]

        # Try each processor in order
        for format_name, processor in processors:
            try:
                temp_result = processor(temp_path)
                result['attempts'].append(f"{format_name}: {temp_result['status']}")

                if temp_result['status'] == 'success':
                    # Success! Use this result
                    result.update(temp_result)
                    result['format_used'] = format_name
                    if file_ext != f'.{format_name.lower()}':
                        result['status'] = f'success (processed as {format_name})'
                    else:
                        result['status'] = f'success ({format_name})'
                    return result
                elif temp_result['status'] == 'password_protected':
                    # Password protected - this is likely the correct format
                    result.update(temp_result)
                    result['format_used'] = format_name
                    result['status'] = f'password_protected ({format_name})'
                    return result

            except Exception as e:
                result['attempts'].append(f"{format_name}: error - {str(e)}")
                continue

        # If we get here, all formats failed
        result['status'] = 'error'
        result['error'] = f'Unable to process as any supported archive format. Attempts: {"; ".join(result["attempts"])}'

        return result

    def _process_zip_file(self, file_path):
        """Process ZIP files with proper Chinese character encoding handling"""
        result = {'file_list': [], 'file_count': 0, 'total_size': 0, 'status': 'success', 'error': ''}

        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                for info in zip_file.infolist():
                    if not info.is_dir():
                        # Fix encoding issues in filename
                        fixed_filename = self._fix_filename_encoding(info.filename)
                        result['file_list'].append(fixed_filename)
                        result['total_size'] += info.file_size
                result['file_count'] = len(result['file_list'])
        except zipfile.BadZipFile:
            result['status'] = 'error'
            result['error'] = 'Corrupted ZIP file'
        except RuntimeError as e:
            if 'password' in str(e).lower():
                result['status'] = 'password_protected'
                result['error'] = 'Password protected'
            else:
                result['status'] = 'error'
                result['error'] = str(e)
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def _process_rar_file(self, file_path):
        """Process RAR files with enhanced error handling and Chinese character encoding"""
        result = {'file_list': [], 'file_count': 0, 'total_size': 0, 'status': 'success', 'error': ''}

        try:
            with rarfile.RarFile(file_path, 'r') as rar_file:
                for info in rar_file.infolist():
                    if not info.is_dir():
                        # Fix encoding issues in filename
                        fixed_filename = self._fix_filename_encoding(info.filename)
                        result['file_list'].append(fixed_filename)
                        result['total_size'] += info.file_size
                result['file_count'] = len(result['file_list'])
        except rarfile.BadRarFile as e:
            result['status'] = 'error'
            error_msg = str(e)
            if 'Not a RAR file' in error_msg:
                result['error'] = 'Not a RAR file (may be different archive format)'
            else:
                result['error'] = f'Corrupted RAR file: {error_msg}'
        except rarfile.PasswordRequired:
            result['status'] = 'password_protected'
            result['error'] = 'Password protected'
        except Exception as e:
            result['status'] = 'error'
            error_msg = str(e)
            if 'Not a RAR file' in error_msg or 'not a rar archive' in error_msg.lower():
                result['error'] = 'Not a RAR file (may be different archive format)'
            else:
                result['error'] = error_msg

        return result

    def _process_7z_file(self, file_path):
        """Process 7Z files with Chinese character encoding handling"""
        result = {'file_list': [], 'file_count': 0, 'total_size': 0, 'status': 'success', 'error': ''}

        try:
            with py7zr.SevenZipFile(file_path, 'r') as sz_file:
                for info in sz_file.list():
                    if not info.is_directory:
                        # Fix encoding issues in filename
                        fixed_filename = self._fix_filename_encoding(info.filename)
                        result['file_list'].append(fixed_filename)
                        result['total_size'] += info.uncompressed if info.uncompressed else 0
                result['file_count'] = len(result['file_list'])
        except py7zr.Bad7zFile:
            result['status'] = 'error'
            result['error'] = 'Corrupted 7Z file'
        except Exception as e:
            if 'password' in str(e).lower():
                result['status'] = 'password_protected'
                result['error'] = 'Password protected'
            else:
                result['status'] = 'error'
                result['error'] = str(e)

        return result

    def _process_tar_file(self, file_path):
        """Process TAR files (including .tar.gz, .tgz) with Chinese character encoding handling"""
        result = {'file_list': [], 'file_count': 0, 'total_size': 0, 'status': 'success', 'error': ''}

        try:
            # Determine if it's compressed
            if file_path.endswith(('.gz', '.tgz')):
                tar_file = tarfile.open(file_path, 'r:gz')
            else:
                tar_file = tarfile.open(file_path, 'r')

            with tar_file:
                for member in tar_file.getmembers():
                    if member.isfile():
                        # Fix encoding issues in filename
                        fixed_filename = self._fix_filename_encoding(member.name)
                        result['file_list'].append(fixed_filename)
                        result['total_size'] += member.size
                result['file_count'] = len(result['file_list'])
        except tarfile.TarError:
            result['status'] = 'error'
            result['error'] = 'Corrupted TAR file'
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def process_ocr_attachment(self, file_data, filename):
        """Process image and PDF attachments with OCR (via OcrService)"""
        result = {
            'extracted_text': '',
            'status': 'success',
            'error': ''
        }

        if not getattr(self, 'ocr_service', None) or not self.ocr_service or not self.ocr_service.is_available():
            result['status'] = 'unavailable'
            result['error'] = f'OCR functionality not available ({self.ocr_status})'
            return result

        try:
            file_ext = Path(filename).suffix.lower()

            if file_ext == '.pdf':
                text = self.ocr_service.extract_text_from_pdf(file_data)
                result['extracted_text'] = text
            elif file_ext in self.image_extensions:
                text = self.ocr_service.extract_text_from_image(file_data)
                result['extracted_text'] = text
            else:
                result['status'] = 'unsupported'
                result['error'] = f'Unsupported file type for OCR: {file_ext}'

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def _process_pdf_ocr(self, file_data):
        """Extract text from PDF using OCR service"""
        result = {'extracted_text': '', 'status': 'success', 'error': ''}
        try:
            if not getattr(self, 'ocr_service', None) or not self.ocr_service:
                raise RuntimeError('OCR service not initialized')
            text = self.ocr_service.extract_text_from_pdf(file_data)
            result['extracted_text'] = text
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
        return result

    def _process_image_ocr(self, file_data):
        """Extract text from image using OCR service"""
        result = {'extracted_text': '', 'status': 'success', 'error': ''}
        try:
            if not getattr(self, 'ocr_service', None) or not self.ocr_service:
                raise RuntimeError('OCR service not initialized')
            text = self.ocr_service.extract_text_from_image(file_data)
            result['extracted_text'] = text
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
        return result

    def process_document_attachment(self, file_data, filename):
        """Process Office document attachments"""
        result = {
            'extracted_content': '',
            'status': 'success',
            'error': ''
        }

        if not OFFICE_AVAILABLE:
            result['status'] = 'unavailable'
            result['error'] = 'Office document processing not available'
            return result

        try:
            file_ext = Path(filename).suffix.lower()

            if file_ext in ['.docx']:
                result = self._process_docx_file(file_data)
            elif file_ext in ['.doc']:
                result['status'] = 'unsupported'
                result['error'] = 'Legacy .doc format not supported, use .docx'
            elif file_ext in ['.pptx']:
                result = self._process_pptx_file(file_data)
            elif file_ext in ['.ppt']:
                result['status'] = 'unsupported'
                result['error'] = 'Legacy .ppt format not supported, use .pptx'
            elif file_ext in ['.xlsx']:
                result = self._process_xlsx_file(file_data)
            elif file_ext in ['.xls']:
                result = self._process_xls_file(file_data)
            else:
                result['status'] = 'unsupported'
                result['error'] = f'Unsupported document format: {file_ext}'

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def _process_docx_file(self, file_data):
        """Extract text from DOCX files"""
        result = {'extracted_content': '', 'status': 'success', 'error': ''}

        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_file.write(file_data)
                temp_path = temp_file.name

            try:
                doc = Document(temp_path)
                paragraphs = []

                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        paragraphs.append(paragraph.text)

                # Also extract text from tables
                for table in doc.tables:
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text.append(cell.text.strip())
                        if row_text:
                            paragraphs.append(' | '.join(row_text))

                result['extracted_content'] = '\n'.join(paragraphs)

            finally:
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def _process_pptx_file(self, file_data):
        """Extract text from PPTX files"""
        result = {'extracted_content': '', 'status': 'success', 'error': ''}

        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_file:
                temp_file.write(file_data)
                temp_path = temp_file.name

            try:
                prs = Presentation(temp_path)
                slides_text = []

                for slide_num, slide in enumerate(prs.slides, 1):
                    slide_content = [f"=== Slide {slide_num} ==="]

                    for shape in slide.shapes:
                        if hasattr(shape, "text") and shape.text.strip():
                            slide_content.append(shape.text)

                    if len(slide_content) > 1:  # More than just the slide header
                        slides_text.append('\n'.join(slide_content))

                result['extracted_content'] = '\n\n'.join(slides_text)

            finally:
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def _process_xlsx_file(self, file_data):
        """Extract text from XLSX files"""
        result = {'extracted_content': '', 'status': 'success', 'error': ''}

        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                temp_file.write(file_data)
                temp_path = temp_file.name

            try:
                from openpyxl import load_workbook

                # Suppress openpyxl warnings during file loading
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    wb = load_workbook(temp_path, data_only=True)

                sheets_content = []

                for sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    sheet_content = [f"=== Sheet: {sheet_name} ==="]

                    for row in ws.iter_rows(values_only=True):
                        row_data = []
                        for cell in row:
                            if cell is not None:
                                # Handle date serial value issues
                                try:
                                    cell_str = str(cell)
                                    # Check for problematic date serial values
                                    if isinstance(cell, (int, float)) and cell > 1000000:
                                        # Likely a problematic date serial, convert to string
                                        cell_str = f"DATE({int(cell)})"
                                    row_data.append(cell_str)
                                except:
                                    row_data.append(str(cell))
                            else:
                                row_data.append('')

                        if any(cell.strip() for cell in row_data):
                            sheet_content.append(' | '.join(row_data))

                    if len(sheet_content) > 1:
                        sheets_content.append('\n'.join(sheet_content))

                result['extracted_content'] = '\n\n'.join(sheets_content)

            finally:
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def _process_xls_file(self, file_data):
        """Extract text from XLS files"""
        result = {'extracted_content': '', 'status': 'success', 'error': ''}

        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.xls', delete=False) as temp_file:
                temp_file.write(file_data)
                temp_path = temp_file.name

            try:
                # Suppress xlrd warnings during file loading
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    workbook = xlrd.open_workbook(temp_path)

                sheets_content = []

                for sheet_name in workbook.sheet_names():
                    sheet = workbook.sheet_by_name(sheet_name)
                    sheet_content = [f"=== Sheet: {sheet_name} ==="]

                    for row_idx in range(sheet.nrows):
                        row_data = []
                        for col_idx in range(sheet.ncols):
                            try:
                                cell_value = sheet.cell_value(row_idx, col_idx)
                                # Handle potential date serial value issues
                                if isinstance(cell_value, float) and cell_value > 1000000:
                                    cell_str = f"DATE({int(cell_value)})"
                                else:
                                    cell_str = str(cell_value) if cell_value else ''
                                row_data.append(cell_str)
                            except:
                                row_data.append('')

                        if any(cell.strip() for cell in row_data):
                            sheet_content.append(' | '.join(row_data))

                    if len(sheet_content) > 1:
                        sheets_content.append('\n'.join(sheet_content))

                result['extracted_content'] = '\n\n'.join(sheets_content)

            finally:
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def extract_embedded_images(self, msg):
        """Extract embedded images from email body content for OCR processing"""
        embedded_images = []

        try:
            if msg.is_multipart():
                # First, collect all parts and build a CID mapping
                cid_parts = {}  # Maps Content-ID to part data

                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition", ""))
                    content_id = part.get("Content-ID", "").strip('<>')

                    # Check if this is an embedded image (inline, not attachment)
                    is_image = content_type.startswith('image/')
                    is_inline = "inline" in content_disposition or (content_id and "attachment" not in content_disposition)

                    if is_image and (is_inline or content_id):
                        try:
                            payload = part.get_payload(decode=True)
                            if payload:
                                image_info = {
                                    'content_type': content_type,
                                    'content_id': content_id,
                                    'filename': part.get_filename() or f"embedded_image_{len(embedded_images)+1}",
                                    'data': payload,
                                    'size': len(payload),
                                    'source': 'cid_attachment'
                                }
                                embedded_images.append(image_info)

                                # Also store in CID mapping for reference lookup
                                if content_id:
                                    cid_parts[content_id] = image_info

                        except Exception as e:
                            print(f"Error extracting embedded image: {e}")
                            continue

                # Now look for base64 embedded images in HTML content
                for part in msg.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/html":
                        try:
                            payload = part.get_payload(decode=True)
                            if payload:
                                charset = part.get_content_charset() or 'utf-8'
                                html_content = payload.decode(charset, errors='ignore')

                                # Extract base64 data URLs
                                base64_images = self._extract_base64_images_from_html(html_content)
                                embedded_images.extend(base64_images)

                        except Exception as e:
                            print(f"Error processing HTML for embedded images: {e}")
                            continue

        except Exception as e:
            print(f"Error in extract_embedded_images: {e}")

        return embedded_images

    def _extract_base64_images_from_html(self, html_content):
        """Extract base64 encoded images from HTML content"""
        base64_images = []

        try:
            # Pattern to match data URLs: data:image/type;base64,data
            data_url_pattern = r'data:image/([^;]+);base64,([A-Za-z0-9+/=]+)'
            matches = re.findall(data_url_pattern, html_content, re.IGNORECASE)

            for i, (image_type, base64_data) in enumerate(matches):
                try:
                    # Decode base64 data
                    image_data = base64.b64decode(base64_data)

                    image_info = {
                        'content_type': f'image/{image_type}',
                        'content_id': '',
                        'filename': f'base64_embedded_image_{i+1}.{image_type}',
                        'data': image_data,
                        'size': len(image_data),
                        'source': 'base64_data_url'
                    }
                    base64_images.append(image_info)

                except Exception as e:
                    print(f"Error decoding base64 image {i+1}: {e}")
                    continue

        except Exception as e:
            print(f"Error extracting base64 images: {e}")

        return base64_images

    def process_embedded_images_ocr(self, embedded_images):
        """Process embedded images through OCR and return extracted text"""
        ocr_results = []

        if not embedded_images:
            return ocr_results

        # Check if OCR service is available
        if not getattr(self, 'ocr_service', None) or not self.ocr_service:
            return [{'filename': img['filename'], 'text': '', 'error': 'OCR service not available'} for img in embedded_images]

        for img in embedded_images:
            try:
                # Process image through OCR
                ocr_result = self.ocr_service.extract_text_from_image(img['data'])

                result = {
                    'filename': img['filename'],
                    'source': img['source'],
                    'content_type': img['content_type'],
                    'size': img['size'],
                    'text': ocr_result,
                    'error': ''
                }

                if ocr_result and ocr_result.strip():
                    print(f"OCR extracted text from {img['filename']}: {ocr_result[:100]}...")

                ocr_results.append(result)

            except Exception as e:
                error_msg = f"OCR processing failed: {str(e)}"
                print(f"Error processing {img['filename']}: {error_msg}")

                result = {
                    'filename': img['filename'],
                    'source': img['source'],
                    'content_type': img['content_type'],
                    'size': img['size'],
                    'text': '',
                    'error': error_msg
                }
                ocr_results.append(result)

        return ocr_results

    def parse_eml_file(self, file_path):
        """Parse a single .eml file and extract all information"""
        try:
            # Read the file
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # Detect encoding
            encoding = self.detect_encoding(raw_data)
            
            # Parse the email
            try:
                email_content = raw_data.decode(encoding, errors='ignore')
            except:
                email_content = raw_data.decode('utf-8', errors='ignore')
            
            msg = email.message_from_string(email_content)
            
            # Extract all information
            result = {
                'filename': os.path.basename(file_path),
                'file_path': str(file_path),
                'subject': self.decode_header(msg.get('Subject', '')),
                'from_email': '',
                'from_name': '',
                'from_full': '',
                'to_emails': '',
                'to_names': '',
                'to_full': '',
                'cc_emails': '',
                'cc_names': '',
                'cc_full': '',
                'bcc_emails': '',
                'bcc_names': '',
                'bcc_full': '',
                'date': '',
                'message_id': msg.get('Message-ID', ''),
                'reply_to': self.decode_header(msg.get('Reply-To', '')),
                'priority': msg.get('X-Priority', ''),
                'importance': msg.get('Importance', ''),
                'content_type': msg.get_content_type(),
                'body': '',
                'attachments_count': 0,
                'attachments_names': '',
                'attachments_sizes': '',
                'total_attachments_size': 0,
                # New comprehensive attachment processing fields
                'archive_file_list': '',
                'archive_file_count': 0,
                'archive_total_size': 0,
                'ocr_extracted_text': '',
                'document_content': '',
                'attachment_processing_status': '',
                'attachment_processing_time': 0,
                # New embedded image OCR fields
                'embedded_image_ocr_text': '',
                'embedded_images_count': 0,
                'embedded_images_info': '',
                'encoding_detected': encoding,
                'has_html': False,
                'error': ''
            }
            
            # Parse From field
            from_addresses = self.extract_email_addresses(msg.get('From', ''))
            if from_addresses:
                result['from_email'] = from_addresses[0]['email']
                result['from_name'] = from_addresses[0]['name']
                result['from_full'] = from_addresses[0]['full']
            
            # Parse To field
            to_addresses = self.extract_email_addresses(msg.get('To', ''))
            if to_addresses:
                result['to_emails'] = '; '.join([addr['email'] for addr in to_addresses])
                result['to_names'] = '; '.join([addr['name'] for addr in to_addresses if addr['name']])
                result['to_full'] = '; '.join([addr['full'] for addr in to_addresses])
            
            # Parse CC field
            cc_addresses = self.extract_email_addresses(msg.get('Cc', ''))
            if cc_addresses:
                result['cc_emails'] = '; '.join([addr['email'] for addr in cc_addresses])
                result['cc_names'] = '; '.join([addr['name'] for addr in cc_addresses if addr['name']])
                result['cc_full'] = '; '.join([addr['full'] for addr in cc_addresses])
            
            # Parse BCC field
            bcc_addresses = self.extract_email_addresses(msg.get('Bcc', ''))
            if bcc_addresses:
                result['bcc_emails'] = '; '.join([addr['email'] for addr in bcc_addresses])
                result['bcc_names'] = '; '.join([addr['name'] for addr in bcc_addresses if addr['name']])
                result['bcc_full'] = '; '.join([addr['full'] for addr in bcc_addresses])
            
            # Parse date
            date_header = msg.get('Date', '')
            if date_header:
                try:
                    parsed_date = email.utils.parsedate_to_datetime(date_header)
                    result['date'] = parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    result['date'] = str(date_header)
            
            # Extract body
            body = self.get_email_body(msg)
            result['body'] = body
            # Check if we had HTML content during extraction (stored by get_email_body)
            result['has_html'] = getattr(self, '_last_had_html', False)
            
            # Extract and process embedded images for OCR
            embedded_images = self.extract_embedded_images(msg)
            embedded_ocr_results = self.process_embedded_images_ocr(embedded_images)

            result['embedded_images_count'] = len(embedded_images)
            if embedded_ocr_results:
                # Create summary of embedded image OCR results
                embedded_ocr_texts = []
                embedded_info_texts = []

                for ocr_result in embedded_ocr_results:
                    if ocr_result['text'] and ocr_result['text'].strip():
                        embedded_ocr_texts.append(f"[{ocr_result['filename']}]: {ocr_result['text']}")

                    # Info about the embedded image
                    info = f"{ocr_result['filename']} ({ocr_result['source']}, {ocr_result['size']} bytes)"
                    if ocr_result['error']:
                        info += f" - Error: {ocr_result['error']}"
                    embedded_info_texts.append(info)

                result['embedded_image_ocr_text'] = ' | '.join(embedded_ocr_texts)
                result['embedded_images_info'] = ' | '.join(embedded_info_texts)

            # Extract attachments with comprehensive processing
            attachments = self.get_attachments_info(msg)
            result['attachments_count'] = len(attachments)
            result['attachments_info'] = attachments  # Store detailed attachment info for debugging
            if attachments:
                result['attachments_names'] = '; '.join([att['filename'] for att in attachments])
                result['attachments_sizes'] = '; '.join([f"{att['size']} bytes" for att in attachments])
                result['total_attachments_size'] = sum([att['size'] for att in attachments])

                # Aggregate comprehensive attachment processing results
                archive_file_lists = []
                archive_file_counts = []
                archive_total_sizes = []
                ocr_texts = []
                document_contents = []
                processing_statuses = []
                processing_times = []

                for att in attachments:
                    # Archive information
                    if att.get('archive_file_list'):
                        archive_file_lists.append(f"{att['filename']}: {att['archive_file_list']}")
                    if att.get('archive_file_count', 0) > 0:
                        archive_file_counts.append(f"{att['filename']}: {att['archive_file_count']}")
                    if att.get('archive_total_size', 0) > 0:
                        archive_total_sizes.append(f"{att['filename']}: {att['archive_total_size']} bytes")

                    # OCR text
                    if att.get('ocr_extracted_text'):
                        ocr_texts.append(f"{att['filename']}: {att['ocr_extracted_text']}")

                    # Document content
                    if att.get('document_content'):
                        document_contents.append(f"{att['filename']}: {att['document_content']}")

                    # Processing status
                    if att.get('processing_status') and att['processing_status'] != 'not_processed':
                        status_text = att['processing_status']
                        if att.get('processing_error'):
                            status_text += f" ({att['processing_error']})"
                        processing_statuses.append(f"{att['filename']}: {status_text}")

                    # Processing time
                    if att.get('processing_time', 0) > 0:
                        processing_times.append(att['processing_time'])

                # Set aggregated results
                result['archive_file_list'] = ' | '.join(archive_file_lists)
                result['archive_file_count'] = sum([att.get('archive_file_count', 0) for att in attachments])
                result['archive_total_size'] = sum([att.get('archive_total_size', 0) for att in attachments])
                result['ocr_extracted_text'] = ' | '.join(ocr_texts)
                result['document_content'] = ' | '.join(document_contents)
                result['attachment_processing_status'] = ' | '.join(processing_statuses)
                result['attachment_processing_time'] = sum(processing_times)
            
            return result
            
        except Exception as e:
            return {
                'filename': os.path.basename(file_path),
                'file_path': str(file_path),
                'error': f"Failed to parse: {str(e)}",
                'subject': '',
                'from_email': '',
                'from_name': '',
                'from_full': '',
                'to_emails': '',
                'to_names': '',
                'to_full': '',
                'cc_emails': '',
                'cc_names': '',
                'cc_full': '',
                'bcc_emails': '',
                'bcc_names': '',
                'bcc_full': '',
                'date': '',
                'message_id': '',
                'reply_to': '',
                'priority': '',
                'importance': '',
                'content_type': '',
                'body': '',
                'attachments_count': 0,
                'attachments_names': '',
                'attachments_sizes': '',
                'total_attachments_size': 0,
                # New comprehensive attachment processing fields
                'archive_file_list': '',
                'archive_file_count': 0,
                'archive_total_size': 0,
                'ocr_extracted_text': '',
                'document_content': '',
                'attachment_processing_status': '',
                'attachment_processing_time': 0,
                'encoding_detected': '',
                'has_html': False
            }


class ProcessingThread(QThread):
    """Thread for processing emails in background"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    file_processed = pyqtSignal(str, dict)
    finished_processing = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, file_paths, output_path):
        super().__init__()
        self.file_paths = file_paths
        self.output_path = output_path
        self.parser = EmailParser()
        self.results = []
    
    def run(self):
        """Process all email files"""
        try:
            total_files = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                if self.isInterruptionRequested():
                    break
                
                self.status_updated.emit(f"Processing: {os.path.basename(file_path)}")
                
                # Parse the email file
                result = self.parser.parse_eml_file(file_path)
                self.results.append(result)
                
                self.file_processed.emit(file_path, result)
                
                # Update progress
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
            
            if not self.isInterruptionRequested():
                # Export to Excel
                self.status_updated.emit("Exporting to Excel...")
                self.export_to_excel()
                self.status_updated.emit("Processing completed!")
                self.finished_processing.emit(self.results)
            
        except Exception as e:
            self.error_occurred.emit(f"Processing error: {str(e)}")
    


    def export_to_excel(self):
        """Export results to Excel file with corruption prevention"""
        try:
            # Suppress openpyxl warnings during workbook creation
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "Email Processing Results"

            # Define column headers (including new comprehensive attachment processing columns)
            headers = [
                'Original Filename', 'File Path', 'Subject', 'From Email', 'From Name', 'From Full',
                'To Emails', 'To Names', 'To Full', 'CC Emails', 'CC Names', 'CC Full',
                'BCC Emails', 'BCC Names', 'BCC Full', 'Date', 'Message ID', 'Reply To',
                'Priority', 'Importance', 'Content Type', 'Body Content', 'Attachments Count',
                'Attachment Names', 'Attachment Sizes', 'Total Attachments Size',
                # New comprehensive attachment processing columns
                'Archive File List', 'Archive File Count', 'Archive Total Size',
                'OCR Extracted Text', 'Document Content', 'Attachment Processing Status',
                'Attachment Processing Time',
                # New embedded image OCR columns
                'Embedded Image OCR Text', 'Embedded Images Count', 'Embedded Images Info',
                'Encoding Detected', 'Has HTML', 'Processing Error'
            ]

            # Write headers
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # Write data with sanitization
            for row, result in enumerate(self.results, 2):
                data = [
                    result.get('filename', ''),
                    result.get('file_path', ''),
                    result.get('subject', ''),
                    result.get('from_email', ''),
                    result.get('from_name', ''),
                    result.get('from_full', ''),
                    result.get('to_emails', ''),
                    result.get('to_names', ''),
                    result.get('to_full', ''),
                    result.get('cc_emails', ''),
                    result.get('cc_names', ''),
                    result.get('cc_full', ''),
                    result.get('bcc_emails', ''),
                    result.get('bcc_names', ''),
                    result.get('bcc_full', ''),
                    result.get('date', ''),
                    result.get('message_id', ''),
                    result.get('reply_to', ''),
                    result.get('priority', ''),
                    result.get('importance', ''),
                    result.get('content_type', ''),
                    result.get('body', ''),
                    result.get('attachments_count', 0),
                    result.get('attachments_names', ''),
                    result.get('attachments_sizes', ''),
                    result.get('total_attachments_size', 0),
                    # New comprehensive attachment processing data
                    result.get('archive_file_list', ''),
                    result.get('archive_file_count', 0),
                    result.get('archive_total_size', 0),
                    result.get('ocr_extracted_text', ''),
                    result.get('document_content', ''),
                    result.get('attachment_processing_status', ''),
                    result.get('attachment_processing_time', 0),
                    # New embedded image OCR data
                    result.get('embedded_image_ocr_text', ''),
                    result.get('embedded_images_count', 0),
                    result.get('embedded_images_info', ''),
                    result.get('encoding_detected', ''),
                    result.get('has_html', False),
                    result.get('error', '')
                ]

                # Sanitize and write each cell value
                for col, value in enumerate(data, 1):
                    sanitized_value = self.parser.sanitize_excel_value(value)
                    cell = ws.cell(row=row, column=col, value=sanitized_value)

                    # Set text wrapping for long content columns
                    # Updated column numbers: Body Content (22), Attachment Names (24), Archive File List (27),
                    # OCR Text (30), Document Content (31), Embedded Image OCR Text (34), Embedded Images Info (36)
                    if col in [22, 24, 25, 27, 30, 31, 34, 36]:
                        cell.alignment = Alignment(wrap_text=True, vertical='top')
            
            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Save the file with warning suppression
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                wb.save(self.output_path)
            
        except Exception as e:
            self.error_occurred.emit(f"Excel export error: {str(e)}")


class ThreadedProcessingThread(QThread):
    """Multi-threaded processing thread for improved performance with large batches"""

    # Identical signal signatures as ProcessingThread for compatibility
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    file_processed = pyqtSignal(str, dict)
    finished_processing = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    # New signals for per-thread progress tracking
    thread_status_updated = pyqtSignal(int, str, str)  # thread_id, status, current_file
    thread_progress_updated = pyqtSignal(int, int)     # thread_id, progress_percent
    thread_state_changed = pyqtSignal(int, str)        # thread_id, state (idle/processing/completed/error)

    def __init__(self, file_paths, output_path, max_workers=None):
        super().__init__()
        self.file_paths = file_paths
        self.output_path = output_path
        self.max_workers = max_workers or ThreadingConfiguration.get_optimal_thread_count()
        self.results = []
        self.progress_mutex = QMutex()
        self.completed_files = 0
        self.errors = []

        # Per-thread tracking
        self.thread_states = {}  # thread_id -> state
        self.thread_files = {}   # thread_id -> current_file
        self.thread_progress = {} # thread_id -> progress_percent
        self.thread_mutex = QMutex()  # Protect thread tracking data

    def run(self):
        """Process all email files using thread pool"""
        try:
            total_files = len(self.file_paths)
            self.status_updated.emit(f"Processing with {self.max_workers} threads")

            # Initialize per-thread tracking
            for thread_id in range(self.max_workers):
                self.thread_states[thread_id] = "idle"
                self.thread_files[thread_id] = ""
                self.thread_progress[thread_id] = 0
                self.thread_state_changed.emit(thread_id, "idle")

            # Initialize thread pool
            try:
                self.status_updated.emit(f"Thread pool created with {self.max_workers} workers")
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    # Submit all files for processing with thread tracking
                    future_to_file = {}
                    future_to_thread_id = {}
                    thread_id_counter = 0

                    for file_path in self.file_paths:
                        thread_id = thread_id_counter % self.max_workers
                        future = executor.submit(self.process_single_file_with_tracking, file_path, thread_id)
                        future_to_file[future] = file_path
                        future_to_thread_id[future] = thread_id
                        thread_id_counter += 1

                    # Collect results as they complete
                    for future in as_completed(future_to_file):
                        if self.isInterruptionRequested():
                            # Enhanced cancellation with timeout handling
                            self.status_updated.emit("Cancelling remaining tasks...")
                            cancelled_count = 0
                            running_count = 0

                            # Mark all threads as idle during cancellation
                            for thread_id in range(self.max_workers):
                                self.thread_state_changed.emit(thread_id, "idle")
                                self.thread_status_updated.emit(thread_id, "Cancelled", "")

                            for remaining_future in future_to_file:
                                if remaining_future.cancel():
                                    cancelled_count += 1
                                else:
                                    # Task is already running, wait briefly for completion
                                    running_count += 1
                                    try:
                                        remaining_future.result(timeout=1.0)
                                        thread_logger.info(f"Running task completed during cancellation")
                                    except Exception:
                                        # Task will be abandoned - log for debugging
                                        thread_logger.warning(f"Running task abandoned during cancellation")

                            # Enhanced shutdown with proper error handling
                            try:
                                executor.shutdown(wait=True, cancel_futures=True)
                                thread_logger.info("Thread pool shutdown completed successfully")
                            except Exception as shutdown_error:
                                thread_logger.error(f"Thread pool shutdown failed: {shutdown_error}")
                                # Force termination if graceful shutdown fails
                                try:
                                    import signal
                                    thread_logger.warning("Attempting force termination")
                                    # Note: This is a last resort and should rarely be needed
                                except Exception:
                                    pass

                            self.status_updated.emit(f"Cancelled {cancelled_count} pending, {running_count} running tasks")
                            break

                        file_path = future_to_file[future]
                        thread_id = future_to_thread_id[future]

                        try:
                            result = future.result()

                            # Thread-safe result collection
                            self.progress_mutex.lock()
                            try:
                                self.results.append(result)
                                self.completed_files += 1
                                progress = int(self.completed_files / total_files * 100)
                            finally:
                                self.progress_mutex.unlock()

                            # Update thread status to completed
                            self.thread_state_changed.emit(thread_id, "completed")
                            self.thread_status_updated.emit(thread_id, "Completed", os.path.basename(file_path))
                            self.thread_progress_updated.emit(thread_id, 100)

                            # Emit signals (these are thread-safe in Qt)
                            self.file_processed.emit(file_path, result)
                            self.progress_updated.emit(progress)

                        except Exception as e:
                            error_msg = f"Worker thread failed processing {os.path.basename(file_path)}: {str(e)}"
                            self.errors.append(error_msg)
                            self.error_occurred.emit(error_msg)

                            # Update thread status to error
                            self.thread_state_changed.emit(thread_id, "error")
                            self.thread_status_updated.emit(thread_id, "Error", os.path.basename(file_path))

                            # Log detailed error for debugging
                            thread_logger.error(f"Thread error in {file_path}: {str(e)}", exc_info=True)
                            self.status_updated.emit(f"Thread error: {type(e).__name__} in {os.path.basename(file_path)}")

                    # Clean up thread-local OCR resources
                    self.cleanup_worker_threads()

            except Exception as e:
                # Threading initialization failed - emit error
                fallback_msg = f"Multi-threading initialization failed: {str(e)}"
                self.error_occurred.emit(fallback_msg)
                return

            if not self.isInterruptionRequested():
                # Export to Excel
                self.status_updated.emit("Exporting to Excel...")
                self.export_to_excel()

                # Report completion with performance info
                if self.errors:
                    self.status_updated.emit(f"Processing completed with {len(self.errors)} errors!")
                else:
                    self.status_updated.emit("Processing completed!")

                self.finished_processing.emit(self.results)

        except Exception as e:
            self.error_occurred.emit(f"Threading error: {str(e)}")

    def cleanup_worker_threads(self):
        """Clean up OCR resources in worker threads"""
        try:
            # Signal cleanup to thread-local OCR manager
            thread_local_ocr.cleanup_thread_local()
            self.status_updated.emit("Worker thread cleanup completed")
        except Exception as e:
            self.status_updated.emit(f"Cleanup warning: {str(e)}")

    def process_single_file_with_tracking(self, file_path, thread_id):
        """Process a single email file using thread-local OCR with per-thread tracking"""
        try:
            # Update thread status to processing
            self.thread_state_changed.emit(thread_id, "processing")
            self.thread_status_updated.emit(thread_id, "Processing", os.path.basename(file_path))
            self.thread_progress_updated.emit(thread_id, 0)

            # Check for interruption before starting processing
            if self.isInterruptionRequested():
                self.thread_state_changed.emit(thread_id, "idle")
                return self._create_error_result(file_path, "Processing interrupted")

            # Create thread-local EmailParser with OCR service
            parser = EmailParser()

            # Replace the parser's OCR service with thread-local instance
            if OCR_SERVICE_AVAILABLE:
                parser.ocr_service = thread_local_ocr.get_ocr_service()

            # Update progress to 25% (parser initialized)
            self.thread_progress_updated.emit(thread_id, 25)

            # Check for interruption before OCR operations
            if self.isInterruptionRequested():
                self.thread_state_changed.emit(thread_id, "idle")
                return self._create_error_result(file_path, "Processing interrupted")

            # Update progress to 50% (starting file parsing)
            self.thread_progress_updated.emit(thread_id, 50)

            # Parse the email file
            result = parser.parse_eml_file(file_path)

            # Update progress to 100% (completed)
            self.thread_progress_updated.emit(thread_id, 100)

            return result

        except Exception as e:
            # Update thread status to error
            self.thread_state_changed.emit(thread_id, "error")
            # Return error result
            return self._create_error_result(file_path, f"Processing failed: {str(e)}")

    def process_single_file(self, file_path):
        """Process a single email file using thread-local OCR (legacy method for compatibility)"""
        try:
            # Check for interruption before starting processing
            if self.isInterruptionRequested():
                return self._create_error_result(file_path, "Processing interrupted")

            # Create thread-local EmailParser with OCR service
            parser = EmailParser()

            # Replace the parser's OCR service with thread-local instance
            if OCR_SERVICE_AVAILABLE:
                parser.ocr_service = thread_local_ocr.get_ocr_service()

            # Check for interruption before OCR operations
            if self.isInterruptionRequested():
                return self._create_error_result(file_path, "Processing interrupted")

            # Parse the email file
            result = parser.parse_eml_file(file_path)
            return result

        except Exception as e:
            # Return error result
            return self._create_error_result(file_path, f"Processing failed: {str(e)}")

    def _create_error_result(self, file_path, error_message):
        """Create a standardized error result"""
        return {
            'file_path': file_path,
            'error': error_message,
            'subject': 'Error',
            'sender': 'Error',
            'recipient': 'Error',
            'date': 'Error',
            'body': 'Error',
            'attachments': [],
            'attachment_count': 0,
            'total_attachment_size': 0,
            'attachment_processing_status': 'error',
            'attachment_processing_time': 0,
            'encoding_detected': 'Error',
            'has_html': False
        }

    def export_to_excel(self):
        """Export results to Excel file - identical to ProcessingThread implementation"""
        try:
            # Suppress openpyxl warnings during workbook creation
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "Email Processing Results"

            # Define column headers (including new comprehensive attachment processing columns)
            headers = [
                'File Name', 'Subject', 'Sender', 'Recipient', 'Date', 'Body Preview',
                'Attachment Count', 'Total Attachment Size (MB)', 'Attachment Names',
                'Attachment Processing Status', 'Attachment Processing Time (s)',
                'Encoding Detected', 'Has HTML Content', 'Processing Status'
            ]

            # Write headers with formatting
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # Write data rows
            for row, result in enumerate(self.results, 2):
                try:
                    # Basic email information
                    ws.cell(row=row, column=1, value=os.path.basename(result.get('file_path', '')))
                    ws.cell(row=row, column=2, value=result.get('subject', ''))
                    ws.cell(row=row, column=3, value=result.get('sender', ''))
                    ws.cell(row=row, column=4, value=result.get('recipient', ''))
                    ws.cell(row=row, column=5, value=result.get('date', ''))

                    # Body preview (first 200 characters)
                    body = result.get('body', '')
                    body_preview = body[:200] + "..." if len(body) > 200 else body
                    ws.cell(row=row, column=6, value=body_preview)

                    # Attachment information
                    attachments = result.get('attachments', [])
                    ws.cell(row=row, column=7, value=len(attachments))

                    # Calculate total attachment size
                    total_size = sum(att.get('size', 0) for att in attachments) / (1024 * 1024)  # Convert to MB
                    ws.cell(row=row, column=8, value=round(total_size, 2))

                    # Attachment names
                    attachment_names = ", ".join([att.get('filename', 'Unknown') for att in attachments])
                    ws.cell(row=row, column=9, value=attachment_names)

                    # Attachment processing status and time
                    ws.cell(row=row, column=10, value=result.get('attachment_processing_status', ''))
                    ws.cell(row=row, column=11, value=result.get('attachment_processing_time', 0))

                    # Additional metadata
                    ws.cell(row=row, column=12, value=result.get('encoding_detected', ''))
                    ws.cell(row=row, column=13, value='Yes' if result.get('has_html', False) else 'No')

                    # Processing status
                    status = 'Success' if not result.get('error') else f"Error: {result.get('error', '')}"
                    ws.cell(row=row, column=14, value=status)

                except Exception as e:
                    # If there's an error writing this row, write error info
                    ws.cell(row=row, column=1, value=f"Row {row} Error")
                    ws.cell(row=row, column=14, value=f"Excel write error: {str(e)}")

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                ws.column_dimensions[column_letter].width = adjusted_width

            # Save the file with warning suppression
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                wb.save(self.output_path)

        except Exception as e:
            self.error_occurred.emit(f"Excel export error: {str(e)}")


class EmailProcessorGUI(QMainWindow):
    """Main GUI application for email processing"""

    def __init__(self):
        super().__init__()
        self.file_paths = []
        self.processing_thread = None
        self.parser = EmailParser()  # Initialize parser to check OCR status
        self.thread_indicators = {}  # Initialize thread indicators dictionary
        self.init_ui()

    def init_ui(self):
        """Initialize the modern user interface"""
        self.setWindowTitle("Email Batch Processor")
        self.setGeometry(100, 100, 1000, 750)
        self.setMinimumSize(800, 600)

        # Set application icon
        try:
            self.setWindowIcon(QIcon('app_icon.ico'))
        except:
            pass  # Icon file not found, continue without it

        # Apply modern stylesheet
        self.setStyleSheet(get_modern_stylesheet())

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(24, 24, 24, 24)

        # Header section - compressed for more log space
        header_layout = QVBoxLayout()
        header_layout.setSpacing(8)  # Reduced spacing from default

        # Title - smaller font for compact layout
        title_label = QLabel("Email Batch Processor")
        # Apply smaller title styling instead of default "title" class
        title_font = QFont()
        title_font.setPointSize(14)  # Reduced from larger default
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        main_layout.addLayout(header_layout)

        # File selection group
        file_group = QGroupBox("📁 File Selection")
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(16)

        # File selection buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)

        self.select_files_btn = QPushButton("📄 Select .eml Files")
        self.select_files_btn.clicked.connect(self.select_files)
        self.select_files_btn.setToolTip("Select individual .eml files to process")
        button_layout.addWidget(self.select_files_btn)

        self.select_folder_btn = QPushButton("📂 Select Folder")
        self.select_folder_btn.clicked.connect(self.select_folder)
        apply_button_class(self.select_folder_btn, "secondary")
        self.select_folder_btn.setToolTip("Select a folder containing .eml files")
        button_layout.addWidget(self.select_folder_btn)

        self.clear_files_btn = QPushButton("🗑️ Clear")
        self.clear_files_btn.clicked.connect(self.clear_files)
        apply_button_class(self.clear_files_btn, "secondary")
        self.clear_files_btn.setToolTip("Clear current file selection")
        button_layout.addWidget(self.clear_files_btn)

        file_layout.addLayout(button_layout)

        # Selected files display
        self.files_label = QLabel("No files selected")
        apply_label_class(self.files_label, "caption")
        file_layout.addWidget(self.files_label)

        main_layout.addWidget(file_group)

        # Output settings group
        output_group = QGroupBox("📊 Output Settings")
        output_layout = QVBoxLayout(output_group)
        output_layout.setSpacing(12)

        # Output file selection
        output_file_layout = QHBoxLayout()
        output_file_layout.setSpacing(12)

        output_label = QLabel("Excel Output File:")
        output_file_layout.addWidget(output_label)

        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("Choose where to save the analysis results...")
        output_file_layout.addWidget(self.output_path_edit, 1)

        self.browse_output_btn = QPushButton("📁 Browse")
        self.browse_output_btn.clicked.connect(self.select_output_file)
        apply_button_class(self.browse_output_btn, "secondary")
        self.browse_output_btn.setToolTip("Choose output file location")
        output_file_layout.addWidget(self.browse_output_btn)

        output_layout.addLayout(output_file_layout)
        main_layout.addWidget(output_group)

        # Processing controls
        controls_group = QGroupBox("⚡ Processing Controls")
        controls_layout = QVBoxLayout(controls_group)
        controls_layout.setSpacing(12)

        # Main processing buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(16)

        self.process_btn = QPushButton("🚀 Start Processing")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)
        apply_button_class(self.process_btn, "success")
        self.process_btn.setToolTip("Begin processing selected email files")
        button_layout.addWidget(self.process_btn)

        self.stop_btn = QPushButton("⏹️ Stop Processing")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        apply_button_class(self.stop_btn, "danger")
        self.stop_btn.setToolTip("Stop the current processing operation")
        button_layout.addWidget(self.stop_btn)

        button_layout.addStretch()
        controls_layout.addLayout(button_layout)

        # Threading controls
        threading_layout = QHBoxLayout()
        threading_layout.setSpacing(16)

        # Multi-threading checkbox
        self.multithreading_checkbox = QCheckBox("Enable multi-threading (recommended for large batches)")
        self.multithreading_checkbox.setChecked(True)
        self.multithreading_checkbox.setToolTip("Use multiple threads for faster processing of large batches")
        threading_layout.addWidget(self.multithreading_checkbox)

        # Thread count controls
        thread_count_label = QLabel("Worker threads:")
        threading_layout.addWidget(thread_count_label)

        self.thread_count_spinbox = QSpinBox()
        self.thread_count_spinbox.setMinimum(1)
        self.thread_count_spinbox.setMaximum(8)
        self.thread_count_spinbox.setValue(ThreadingConfiguration.get_optimal_thread_count())
        self.thread_count_spinbox.setToolTip("Number of worker threads for processing")
        self.thread_count_spinbox.valueChanged.connect(self.update_memory_estimate)
        threading_layout.addWidget(self.thread_count_spinbox)

        # Memory usage estimate
        self.memory_label = QLabel("Est. memory: 0.0 GB")
        apply_label_class(self.memory_label, "caption")
        self.memory_label.setToolTip("Estimated additional memory usage for OCR processing")
        threading_layout.addWidget(self.memory_label)

        threading_layout.addStretch()
        controls_layout.addLayout(threading_layout)

        main_layout.addWidget(controls_group)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setTextVisible(True)
        main_layout.addWidget(self.progress_bar)

        # Per-thread progress indicators
        self.thread_indicators_group = QGroupBox("🧵 Thread Status (Multi-threading)")
        self.thread_indicators_layout = QVBoxLayout(self.thread_indicators_group)
        self.thread_indicators_layout.setSpacing(8)

        # Thread indicators are initialized in __init__
        self.thread_indicators_group.setVisible(False)  # Hidden by default
        main_layout.addWidget(self.thread_indicators_group)

        # Status and log area
        status_group = QGroupBox("📈 Processing Status & Logs")
        status_layout = QVBoxLayout(status_group)
        status_layout.setSpacing(12)

        # Current status
        status_header_layout = QHBoxLayout()
        status_icon_label = QLabel("🔄")
        status_header_layout.addWidget(status_icon_label)

        self.status_label = QLabel("Ready to process files")
        apply_label_class(self.status_label, "status-success")
        status_header_layout.addWidget(self.status_label, 1)

        status_layout.addLayout(status_header_layout)

        # Feature status information
        feature_status = self.get_feature_status()
        self.feature_label = QLabel(feature_status)
        apply_label_class(self.feature_label, "caption")
        status_layout.addWidget(self.feature_label)

        # Processing log
        log_label = QLabel("Processing Log:")
        status_layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(180)
        self.log_text.setReadOnly(True)
        self.log_text.setPlaceholderText("Processing logs will appear here...")
        status_layout.addWidget(self.log_text)

        main_layout.addWidget(status_group)

        # Initialize memory estimate (after all UI components are created)
        self.update_memory_estimate()

        # Set default output filename
        default_output = os.path.join(os.getcwd(), f"email_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        self.output_path_edit.setText(default_output)

    def get_feature_status(self):
        """Get status of available features"""
        status_parts = []

        # Archive processing
        archive_count = 1  # ZIP is always available (built-in)
        if RARFILE_AVAILABLE: archive_count += 1
        if PY7ZR_AVAILABLE: archive_count += 1
        archive_emoji = "✅" if archive_count >= 2 else "⚠️"
        status_parts.append(f"{archive_emoji} Archive: {archive_count}/3 formats")

        # OCR processing
        ocr_status = getattr(self.parser, 'ocr_status', '') or ''
        if isinstance(ocr_status, str) and ocr_status.startswith("available"):
            backend = ocr_status.split(":", 1)[1] if ":" in ocr_status else ""
            backend_display = backend.upper() if backend else "Unknown"
            status_parts.append(f"✅ OCR: {backend_display} Ready")
        else:
            status_parts.append("⚠️ OCR: Unavailable")

        # Document processing
        doc_formats = ["DOCX", "XLSX", "PPTX"]
        status_parts.append(f"✅ Documents: {len(doc_formats)} formats")

        return " • ".join(status_parts)

    def select_files(self):
        """Select individual .eml files"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Select .eml Files",
            "",
            "Email Files (*.eml);;All Files (*)"
        )

        if files:
            self.file_paths = files
            self.update_files_display()
            self.update_ui_state()
            self.suggest_threading_for_large_batch()

    def select_folder(self):
        """Select folder containing .eml files"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "Select Folder Containing .eml Files"
        )

        if folder:
            # Find all .eml files in the folder
            eml_files = []
            for root, _, files in os.walk(folder):
                for file in files:
                    if file.lower().endswith('.eml'):
                        eml_files.append(os.path.join(root, file))

            if eml_files:
                self.file_paths = eml_files
                self.update_files_display()
                self.update_ui_state()
                self.suggest_threading_for_large_batch()
            else:
                QMessageBox.information(self, "No Files Found", "No .eml files found in the selected folder.")

    def clear_files(self):
        """Clear selected files"""
        self.file_paths = []
        self.update_files_display()
        self.update_ui_state()

    def update_files_display(self):
        """Update the display of selected files"""
        if not self.file_paths:
            self.files_label.setText("No files selected")
        else:
            count = len(self.file_paths)
            if count == 1:
                self.files_label.setText(f"1 file selected: {os.path.basename(self.file_paths[0])}")
            else:
                self.files_label.setText(f"{count} files selected")

    def select_output_file(self):
        """Select output Excel file location"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Excel File As",
            self.output_path_edit.text(),
            "Excel Files (*.xlsx);;All Files (*)"
        )

        if file_path:
            if not file_path.lower().endswith('.xlsx'):
                file_path += '.xlsx'
            self.output_path_edit.setText(file_path)

    def update_memory_estimate(self):
        """Update the memory usage estimate based on thread count"""
        thread_count = self.thread_count_spinbox.value()
        memory_gb = ThreadingConfiguration.calculate_memory_usage(thread_count)
        self.memory_label.setText(f"Est. memory: {memory_gb:.1f} GB")

        # Update thread indicators if they exist
        self.update_thread_indicators_count(thread_count)

    def create_thread_indicators(self, thread_count):
        """Create visual indicators for each worker thread"""
        # Clear existing indicators
        self.clear_thread_indicators()

        for thread_id in range(thread_count):
            # Create container for this thread
            thread_container = QFrame()
            thread_container.setFrameStyle(QFrame.Shape.StyledPanel)
            thread_layout = QHBoxLayout(thread_container)
            thread_layout.setContentsMargins(8, 4, 8, 4)
            thread_layout.setSpacing(8)

            # Thread ID label
            thread_id_label = QLabel(f"Thread {thread_id + 1}:")
            thread_id_label.setMinimumWidth(70)
            apply_label_class(thread_id_label, "caption")
            thread_layout.addWidget(thread_id_label)

            # Status indicator (colored dot)
            status_dot = QLabel("●")
            status_dot.setStyleSheet("color: #94A3B8; font-size: 14px;")  # Gray for idle
            thread_layout.addWidget(status_dot)

            # Status text
            status_label = QLabel("Idle")
            status_label.setMinimumWidth(80)
            apply_label_class(status_label, "caption")
            thread_layout.addWidget(status_label)

            # Current file label
            file_label = QLabel("")
            file_label.setMinimumWidth(200)
            apply_label_class(file_label, "caption")
            thread_layout.addWidget(file_label, 1)  # Stretch to fill available space

            # Progress bar for this thread
            progress_bar = QProgressBar()
            progress_bar.setMaximumWidth(100)
            progress_bar.setMaximumHeight(16)
            progress_bar.setTextVisible(False)
            progress_bar.setValue(0)
            thread_layout.addWidget(progress_bar)

            # Store references to widgets
            self.thread_indicators[thread_id] = {
                'container': thread_container,
                'status_dot': status_dot,
                'status_label': status_label,
                'file_label': file_label,
                'progress_bar': progress_bar
            }

            # Add to layout
            self.thread_indicators_layout.addWidget(thread_container)

    def clear_thread_indicators(self):
        """Clear all thread indicator widgets"""
        for thread_id, widgets in self.thread_indicators.items():
            widgets['container'].setParent(None)
            widgets['container'].deleteLater()
        self.thread_indicators.clear()

    def update_thread_indicators_count(self, thread_count):
        """Update the number of thread indicators based on thread count"""
        if len(self.thread_indicators) != thread_count:
            self.create_thread_indicators(thread_count)

    def on_thread_status_updated(self, thread_id, status, current_file):
        """Handle per-thread status updates"""
        if thread_id in self.thread_indicators:
            widgets = self.thread_indicators[thread_id]
            widgets['status_label'].setText(status)
            widgets['file_label'].setText(current_file)

    def on_thread_progress_updated(self, thread_id, progress_percent):
        """Handle per-thread progress updates"""
        if thread_id in self.thread_indicators:
            widgets = self.thread_indicators[thread_id]
            widgets['progress_bar'].setValue(progress_percent)

    def on_thread_state_changed(self, thread_id, state):
        """Handle per-thread state changes with visual indicators"""
        if thread_id in self.thread_indicators:
            widgets = self.thread_indicators[thread_id]
            status_dot = widgets['status_dot']

            # Update status dot color based on state
            if state == "idle":
                status_dot.setStyleSheet("color: #94A3B8; font-size: 14px;")  # Gray
            elif state == "processing":
                status_dot.setStyleSheet("color: #2563EB; font-size: 14px;")  # Blue
            elif state == "completed":
                status_dot.setStyleSheet("color: #059669; font-size: 14px;")  # Green
            elif state == "error":
                status_dot.setStyleSheet("color: #DC2626; font-size: 14px;")  # Red

    def show_thread_indicators(self, show=True):
        """Show or hide the thread indicators section"""
        self.thread_indicators_group.setVisible(show)

    def reset_thread_indicators(self):
        """Reset all thread indicators to idle state"""
        for thread_id, widgets in self.thread_indicators.items():
            widgets['status_dot'].setStyleSheet("color: #94A3B8; font-size: 14px;")  # Gray
            widgets['status_label'].setText("Idle")
            widgets['file_label'].setText("")
            widgets['progress_bar'].setValue(0)

    def suggest_threading_for_large_batch(self):
        """Suggest enabling threading for large batches with system resource awareness"""
        file_count = len(self.file_paths)

        # Only suggest if threading would be beneficial and system can handle it
        if (file_count >= 5 and
            not self.multithreading_checkbox.isChecked() and
            ThreadingConfiguration.should_use_threading(file_count)):

            optimal_threads = ThreadingConfiguration.get_dynamic_thread_count(file_count)
            memory_estimate = ThreadingConfiguration.calculate_memory_usage(optimal_threads)

            reply = QMessageBox.question(
                self,
                "Enable Multi-threading?",
                f"Enable multi-threading for better performance?\n\n"
                f"Files to process: {file_count}\n"
                f"Recommended threads: {optimal_threads}\n"
                f"Estimated memory usage: {memory_estimate:.1f} GB\n\n"
                f"This can improve processing speed by 2-3x for OCR-heavy workloads.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.multithreading_checkbox.setChecked(True)
                self.thread_count_spinbox.setValue(optimal_threads)
                self.update_memory_estimate()
                thread_logger.info(f"User enabled threading: {optimal_threads} threads for {file_count} files")
        elif file_count >= 5 and not ThreadingConfiguration.should_use_threading(file_count):
            # Inform user why threading is not recommended
            QMessageBox.information(
                self,
                "Threading Not Recommended",
                "Multi-threading is not recommended due to insufficient system resources.\n\n"
                "This may be due to:\n"
                "• Low available memory\n"
                "• High CPU usage\n"
                "• Single-core system\n\n"
                "Processing will continue in single-threaded mode."
            )

    def update_ui_state(self):
        """Update UI state based on current conditions"""
        has_files = len(self.file_paths) > 0
        has_output = bool(self.output_path_edit.text().strip())
        # Ensure is_processing is always a boolean value
        is_processing = bool(self.processing_thread and self.processing_thread.isRunning())

        self.process_btn.setEnabled(has_files and has_output and not is_processing)
        self.stop_btn.setEnabled(is_processing)
        self.select_files_btn.setEnabled(not is_processing)
        self.select_folder_btn.setEnabled(not is_processing)
        self.clear_files_btn.setEnabled(not is_processing)
        self.browse_output_btn.setEnabled(not is_processing)

        # Disable threading controls during processing
        self.multithreading_checkbox.setEnabled(not is_processing)
        self.thread_count_spinbox.setEnabled(not is_processing)

    def start_processing(self):
        """Start processing the selected files"""
        if not self.file_paths:
            QMessageBox.warning(self, "No Files", "Please select .eml files to process.")
            return

        output_path = self.output_path_edit.text().strip()
        if not output_path:
            QMessageBox.warning(self, "No Output File", "Please specify an output Excel file.")
            return

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Clear log
        self.log_text.clear()

        # Show progress bar
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Record start time for performance monitoring
        self.processing_start_time = time.time()

        # Determine whether to use threading based on user settings and system resources
        user_wants_threading = self.multithreading_checkbox.isChecked()
        system_can_handle_threading = ThreadingConfiguration.should_use_threading(len(self.file_paths))
        use_threading = user_wants_threading and system_can_handle_threading

        if user_wants_threading and not system_can_handle_threading:
            self.log_text.append("⚠️ Threading disabled due to insufficient system resources")
            thread_logger.warning("Threading requested but system resources insufficient")

        # Start appropriate processing thread
        try:
            if use_threading:
                # Use dynamic thread count if user hasn't manually adjusted it
                user_thread_count = self.thread_count_spinbox.value()
                optimal_thread_count = ThreadingConfiguration.get_dynamic_thread_count(len(self.file_paths))

                # Use the minimum of user preference and system optimal
                thread_count = min(user_thread_count, optimal_thread_count)

                if thread_count != user_thread_count:
                    self.log_text.append(f"Thread count adjusted from {user_thread_count} to {thread_count} based on system resources")

                self.processing_thread = ThreadedProcessingThread(
                    self.file_paths,
                    output_path,
                    max_workers=thread_count
                )

                # Create and show thread indicators for multi-threading
                self.create_thread_indicators(thread_count)
                self.show_thread_indicators(True)

                self.log_text.append(f"Processing with {thread_count} threads (dynamic scaling enabled)")
            else:
                self.processing_thread = ProcessingThread(self.file_paths, output_path)

                # Hide thread indicators for single-threaded processing
                self.show_thread_indicators(False)

                if len(self.file_paths) < 5:
                    self.log_text.append("Processing single-threaded (small batch)")
                else:
                    self.log_text.append("Processing single-threaded")

            # Connect signals (identical for both thread types)
            self.processing_thread.progress_updated.connect(self.progress_bar.setValue)
            self.processing_thread.status_updated.connect(self.status_label.setText)
            self.processing_thread.file_processed.connect(self.on_file_processed)
            self.processing_thread.finished_processing.connect(self.on_processing_finished)
            self.processing_thread.error_occurred.connect(self.on_error_occurred)

            # Connect per-thread signals if using threaded processing
            if use_threading and hasattr(self.processing_thread, 'thread_status_updated'):
                self.processing_thread.thread_status_updated.connect(self.on_thread_status_updated)
                self.processing_thread.thread_progress_updated.connect(self.on_thread_progress_updated)
                self.processing_thread.thread_state_changed.connect(self.on_thread_state_changed)

            self.processing_thread.start()
            self.update_ui_state()

            self.log_text.append(f"Started processing {len(self.file_paths)} files...")

        except Exception as e:
            # If threading initialization fails, fall back to single-threaded
            error_msg = f"Multi-threading initialization failed: {str(e)}"
            self.log_text.append(error_msg)
            QMessageBox.warning(
                self,
                "Threading Error",
                "Multi-threading initialization failed. Falling back to single-threaded processing."
            )

            # Fall back to single-threaded processing
            self.processing_thread = ProcessingThread(self.file_paths, output_path)
            self.processing_thread.progress_updated.connect(self.progress_bar.setValue)
            self.processing_thread.status_updated.connect(self.status_label.setText)
            self.processing_thread.file_processed.connect(self.on_file_processed)
            self.processing_thread.finished_processing.connect(self.on_processing_finished)
            self.processing_thread.error_occurred.connect(self.on_error_occurred)

            self.processing_thread.start()
            self.update_ui_state()
            self.log_text.append("Fallback: Processing single-threaded")

    def stop_processing(self):
        """Stop the processing thread with enhanced cleanup for threading"""
        if self.processing_thread and self.processing_thread.isRunning():
            # Determine if we're using threaded processing
            is_threaded = isinstance(self.processing_thread, ThreadedProcessingThread)

            if is_threaded:
                self.log_text.append("Stopping multi-threaded processing...")
                self.status_label.setText("Terminating worker threads...")
            else:
                self.log_text.append("Stopping single-threaded processing...")
                self.status_label.setText("Stopping processing...")

            # Request interruption
            self.processing_thread.requestInterruption()

            # For threaded processing, give more time for cleanup
            wait_time = 5000 if is_threaded else 3000
            thread_stopped = self.processing_thread.wait(wait_time)

            if not thread_stopped:
                self.log_text.append("⚠️ Warning: Thread did not stop gracefully")
                # Force terminate if needed
                try:
                    self.processing_thread.terminate()
                    self.processing_thread.wait(1000)
                except Exception as e:
                    self.log_text.append(f"⚠️ Thread termination error: {str(e)}")

            # Update UI
            self.status_label.setText("Processing stopped by user")
            self.progress_bar.setVisible(False)

            # Reset and hide thread indicators if they were shown
            if is_threaded:
                self.reset_thread_indicators()
                self.show_thread_indicators(False)

            self.update_ui_state()

            # Log completion with thread type
            if is_threaded:
                self.log_text.append("✅ Multi-threaded processing stopped. OCR resources cleaned up.")
            else:
                self.log_text.append("✅ Processing stopped by user.")

            # Clean up thread reference
            self.processing_thread = None

    def on_file_processed(self, file_path, result):
        """Handle when a file is processed"""
        filename = os.path.basename(file_path)
        if result.get('error'):
            self.log_text.append(f"❌ {filename}: {result['error']}")
        else:
            subject = result.get('subject', 'No Subject')[:50]
            if len(result.get('subject', '')) > 50:
                subject += "..."
            self.log_text.append(f"✅ {filename}: {subject}")

        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_processing_finished(self, results):
        """Handle when processing is finished"""
        self.progress_bar.setVisible(False)

        # Determine if threading was used and handle thread indicators
        threading_used = isinstance(self.processing_thread, ThreadedProcessingThread)
        if threading_used:
            # Hide thread indicators after a brief delay to show completion
            from PyQt6.QtCore import QTimer
            timer = QTimer()
            timer.singleShot(2000, lambda: self.show_thread_indicators(False))

        self.update_ui_state()

        # Calculate performance metrics
        processing_time = time.time() - self.processing_start_time
        total_files = len(results)
        successful = len([r for r in results if not r.get('error')])
        failed = total_files - successful
        throughput = total_files / processing_time if processing_time > 0 else 0

        if threading_used:
            thread_count = self.processing_thread.max_workers
            performance_info = f"Completed in {processing_time:.1f} seconds using {thread_count} threads"
        else:
            performance_info = f"Completed in {processing_time:.1f} seconds (single-threaded)"

        # Enhanced summary with performance metrics
        summary = f"\nProcessing completed!\n"
        summary += f"Total files: {total_files}\n"
        summary += f"Successfully processed: {successful}\n"
        summary += f"Failed: {failed}\n"
        summary += f"Processing time: {processing_time:.1f} seconds\n"
        summary += f"Throughput: {throughput:.1f} files/sec\n"
        summary += f"Output saved to: {self.output_path_edit.text()}"

        self.log_text.append(summary)
        self.log_text.append(performance_info)

        # Enhanced completion message with performance info
        completion_msg = f"Processing completed!\n\n"
        completion_msg += f"Successfully processed: {successful}/{total_files} files\n"
        completion_msg += f"Time: {processing_time:.1f}s"
        if threading_used:
            completion_msg += f" ({thread_count} threads)"
        completion_msg += f"\nThroughput: {throughput:.1f} files/sec\n"
        completion_msg += f"Output: {os.path.basename(self.output_path_edit.text())}"

        QMessageBox.information(
            self,
            "Processing Complete",
            completion_msg
        )

    def on_error_occurred(self, error_message):
        """Handle processing errors"""
        self.progress_bar.setVisible(False)

        # Hide thread indicators if they were shown
        threading_used = isinstance(self.processing_thread, ThreadedProcessingThread)
        if threading_used:
            self.reset_thread_indicators()
            self.show_thread_indicators(False)

        self.update_ui_state()
        self.status_label.setText("Error occurred during processing")

        self.log_text.append(f"❌ Error: {error_message}")

        QMessageBox.critical(self, "Processing Error", f"An error occurred:\n\n{error_message}")


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Email Batch Processor")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Email Tools")

    # Create and show main window
    window = EmailProcessorGUI()
    window.show()

    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
