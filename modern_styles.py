"""
Modern UI stylesheet and styling utilities for Email Batch Processor
Provides a cohesive, professional design system
"""

# Modern color palette
COLORS = {
    # Primary colors
    'primary': '#2563EB',      # Blue
    'primary_hover': '#1D4ED8', # Darker blue
    'primary_pressed': '#1E40AF', # Even darker blue for pressed state
    'primary_light': '#DBEAFE', # Light blue
    
    # Secondary colors
    'secondary': '#0891B2',     # Teal
    'secondary_hover': '#0E7490', # Darker teal
    'secondary_light': '#CFFAFE', # Light teal
    
    # Neutral colors
    'background': '#FFFFFF',    # White
    'surface': '#F8FAFC',      # Light gray
    'surface_hover': '#F1F5F9', # Slightly darker gray
    'border': '#E2E8F0',       # Light border
    'text_primary': '#1E293B',  # Dark gray
    'text_secondary': '#64748B', # Medium gray
    'text_muted': '#94A3B8',    # Light gray
    
    # Status colors
    'success': '#059669',       # Green
    'success_light': '#D1FAE5', # Light green
    'warning': '#D97706',       # Orange
    'warning_light': '#FED7AA', # Light orange
    'error': '#DC2626',         # Red
    'error_light': '#FEE2E2',   # Light red
    'info': '#0891B2',          # Teal
    'info_light': '#CFFAFE',    # Light teal
}

def get_modern_stylesheet():
    """Return the complete modern stylesheet"""
    return f"""
    /* Main Window */
    QMainWindow {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        font-family: 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
    }}
    
    /* Group Boxes */
    QGroupBox {{
        font-weight: 600;
        font-size: 15px;
        color: {COLORS['text_primary']};
        border: 2px solid {COLORS['border']};
        border-radius: 12px;
        margin-top: 12px;
        padding-top: 8px;
        background-color: {COLORS['surface']};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 16px;
        padding: 0 8px 0 8px;
        color: {COLORS['primary']};
        background-color: {COLORS['surface']};
    }}
    
    /* Primary Buttons */
    QPushButton {{
        background-color: {COLORS['primary']};
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        min-height: 20px;
    }}
    
    QPushButton:hover {{
        background-color: {COLORS['primary_hover']};
        border: 2px solid {COLORS['primary']};
    }}

    QPushButton:pressed {{
        background-color: {COLORS['primary_pressed']};
        border: 2px solid {COLORS['primary_pressed']};
    }}
    
    QPushButton:disabled {{
        background-color: {COLORS['text_muted']};
        color: white;
    }}
    
    /* Secondary Buttons */
    QPushButton[class="secondary"] {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 2px solid {COLORS['border']};
    }}
    
    QPushButton[class="secondary"]:hover {{
        background-color: {COLORS['surface_hover']};
        border-color: {COLORS['primary']};
        color: {COLORS['primary']};
    }}
    
    /* Danger Buttons */
    QPushButton[class="danger"] {{
        background-color: {COLORS['error']};
        color: white;
    }}
    
    QPushButton[class="danger"]:hover {{
        background-color: #B91C1C;
    }}
    
    /* Success Buttons */
    QPushButton[class="success"] {{
        background-color: {COLORS['success']};
        color: white;
    }}
    
    QPushButton[class="success"]:hover {{
        background-color: #047857;
    }}
    
    /* Line Edits */
    QLineEdit {{
        border: 2px solid {COLORS['border']};
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        background-color: white;
        color: {COLORS['text_primary']};
    }}
    
    QLineEdit:focus {{
        border-color: {COLORS['primary']};
        outline: none;
    }}
    
    QLineEdit:disabled {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_muted']};
    }}
    
    /* Text Edits */
    QTextEdit {{
        border: 2px solid {COLORS['border']};
        border-radius: 8px;
        padding: 12px;
        font-size: 13px;
        background-color: white;
        color: {COLORS['text_primary']};
        font-family: 'Consolas', 'Monaco', monospace;
    }}
    
    QTextEdit:focus {{
        border-color: {COLORS['primary']};
    }}
    
    /* Progress Bar */
    QProgressBar {{
        border: 2px solid {COLORS['border']};
        border-radius: 8px;
        text-align: center;
        font-weight: 600;
        font-size: 13px;
        color: {COLORS['text_primary']};
        background-color: {COLORS['surface']};
        height: 24px;
    }}
    
    QProgressBar::chunk {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                   stop:0 {COLORS['primary']}, 
                                   stop:1 {COLORS['secondary']});
        border-radius: 6px;
        margin: 2px;
    }}
    
    /* Labels */
    QLabel {{
        color: {COLORS['text_primary']};
        font-size: 14px;
    }}
    
    QLabel[class="title"] {{
        font-size: 28px;
        font-weight: 700;
        color: {COLORS['primary']};
        margin: 16px 0;
    }}
    
    QLabel[class="subtitle"] {{
        font-size: 16px;
        font-weight: 500;
        color: {COLORS['text_secondary']};
        margin: 8px 0;
    }}
    
    QLabel[class="caption"] {{
        font-size: 12px;
        color: {COLORS['text_muted']};
        font-style: italic;
    }}
    
    QLabel[class="status-success"] {{
        color: {COLORS['success']};
        font-weight: 600;
    }}
    
    QLabel[class="status-warning"] {{
        color: {COLORS['warning']};
        font-weight: 600;
    }}
    
    QLabel[class="status-error"] {{
        color: {COLORS['error']};
        font-weight: 600;
    }}
    
    /* Checkboxes */
    QCheckBox {{
        color: {COLORS['text_primary']};
        font-size: 14px;
        spacing: 8px;
    }}
    
    QCheckBox::indicator {{
        width: 18px;
        height: 18px;
        border: 2px solid {COLORS['border']};
        border-radius: 4px;
        background-color: white;
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {COLORS['primary']};
        border-color: {COLORS['primary']};
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
    }}
    
    QCheckBox::indicator:hover {{
        border-color: {COLORS['primary']};
    }}
    
    /* Scroll Bars */
    QScrollBar:vertical {{
        background: {COLORS['surface']};
        width: 12px;
        border-radius: 6px;
        margin: 0;
    }}
    
    QScrollBar::handle:vertical {{
        background: {COLORS['text_muted']};
        border-radius: 6px;
        min-height: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background: {COLORS['text_secondary']};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    /* Tooltips */
    QToolTip {{
        background-color: {COLORS['text_primary']};
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 12px;
    }}
    """

def apply_button_class(button, class_name):
    """Apply a CSS class to a button"""
    button.setProperty("class", class_name)
    button.style().unpolish(button)
    button.style().polish(button)

def apply_label_class(label, class_name):
    """Apply a CSS class to a label"""
    label.setProperty("class", class_name)
    label.style().unpolish(label)
    label.style().polish(label)
