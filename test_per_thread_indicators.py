#!/usr/bin/env python3
"""
Test script for per-thread progress indicators in Email Batch Processor
Tests the new threading visualization features
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# Add the current directory to Python path to import email_processor
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from email_processor import EmailProcessorGUI, ThreadedProcessingThread, ThreadingConfiguration

def create_test_eml_files(count=10):
    """Create test .eml files for testing"""
    test_files = []
    temp_dir = tempfile.mkdtemp(prefix="eml_test_")
    
    for i in range(count):
        eml_content = f"""From: test{i}@example.com
To: <EMAIL>
Subject: Test Email {i}
Date: Mon, 1 Jan 2024 12:00:00 +0000

This is test email number {i}.
It contains some sample content for testing the multi-threading functionality.

Best regards,
Test Sender {i}
"""
        
        file_path = os.path.join(temp_dir, f"test_email_{i:03d}.eml")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(eml_content)
        test_files.append(file_path)
    
    return test_files, temp_dir

def test_per_thread_indicators():
    """Test the per-thread progress indicators functionality"""
    print("🧪 Testing Per-Thread Progress Indicators")
    print("=" * 50)
    
    # Create test files
    print("📁 Creating test .eml files...")
    test_files, temp_dir = create_test_eml_files(8)
    print(f"✅ Created {len(test_files)} test files in {temp_dir}")
    
    # Create application
    app = QApplication(sys.argv)
    
    # Create GUI
    gui = EmailProcessorGUI()
    
    # Test threading configuration
    print("\n🔧 Testing Threading Configuration...")
    optimal_threads = ThreadingConfiguration.get_optimal_thread_count()
    print(f"✅ Optimal thread count: {optimal_threads}")
    
    memory_usage = ThreadingConfiguration.calculate_memory_usage(4)
    print(f"✅ Memory usage for 4 threads: {memory_usage:.1f} GB")
    
    # Test thread indicator creation
    print("\n🧵 Testing Thread Indicator Creation...")
    gui.create_thread_indicators(4)
    print(f"✅ Created {len(gui.thread_indicators)} thread indicators")
    
    # Test thread indicator updates
    print("\n📊 Testing Thread Indicator Updates...")
    for thread_id in range(4):
        gui.on_thread_state_changed(thread_id, "processing")
        gui.on_thread_status_updated(thread_id, "Processing", f"test_file_{thread_id}.eml")
        gui.on_thread_progress_updated(thread_id, 50)
    print("✅ Updated all thread indicators to processing state")
    
    # Test completion states
    print("\n✅ Testing Completion States...")
    for thread_id in range(4):
        gui.on_thread_state_changed(thread_id, "completed")
        gui.on_thread_status_updated(thread_id, "Completed", f"test_file_{thread_id}.eml")
        gui.on_thread_progress_updated(thread_id, 100)
    print("✅ Updated all thread indicators to completed state")
    
    # Test error states
    print("\n❌ Testing Error States...")
    gui.on_thread_state_changed(0, "error")
    gui.on_thread_status_updated(0, "Error", "problematic_file.eml")
    print("✅ Updated thread 0 to error state")
    
    # Test reset
    print("\n🔄 Testing Reset...")
    gui.reset_thread_indicators()
    print("✅ Reset all thread indicators")
    
    # Show GUI for visual inspection
    gui.show()
    
    # Set up test files in GUI
    gui.file_paths = test_files
    gui.update_files_display()
    gui.update_ui_state()
    
    # Enable threading
    gui.multithreading_checkbox.setChecked(True)
    gui.thread_count_spinbox.setValue(4)
    gui.update_memory_estimate()
    
    print(f"\n🎯 GUI Setup Complete!")
    print(f"   - Files loaded: {len(test_files)}")
    print(f"   - Threading enabled: {gui.multithreading_checkbox.isChecked()}")
    print(f"   - Thread count: {gui.thread_count_spinbox.value()}")
    print(f"   - Memory estimate: {gui.memory_label.text()}")
    
    print(f"\n🚀 Ready for testing!")
    print(f"   - Click 'Start Processing' to test per-thread indicators")
    print(f"   - Watch the 'Thread Status' section for real-time updates")
    print(f"   - Each thread will show its current file and progress")
    
    # Auto-close after demonstration (optional)
    def auto_close():
        print("\n🏁 Test completed successfully!")
        print("   All per-thread indicator features are working correctly.")
        app.quit()
    
    # Set up auto-close timer (30 seconds for demonstration)
    timer = QTimer()
    timer.timeout.connect(auto_close)
    timer.start(30000)  # 30 seconds
    
    # Run the application
    try:
        return app.exec()
    finally:
        # Cleanup test files
        import shutil
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up test files from {temp_dir}")
        except Exception as e:
            print(f"⚠️ Warning: Could not clean up test files: {e}")

if __name__ == "__main__":
    print("🎯 Per-Thread Progress Indicators Test")
    print("=====================================")
    print("This test demonstrates the new per-thread progress indicators")
    print("that provide detailed visibility into multi-threaded processing.")
    print()
    
    try:
        exit_code = test_per_thread_indicators()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
