"""
PaddleOCR-based OCR Service for Email Batch Processor
Provides robust OCR functionality with English and Chinese support
"""

import io
import logging
import numpy as np
from typing import List, Optional
from dataclasses import dataclass

# Configure logging to reduce PaddleOCR verbosity
logging.getLogger('ppocr').setLevel(logging.WARNING)
logging.getLogger('paddle').setLevel(logging.WARNING)

# Optional imports (performed lazily)
try:
    from PIL import Image
    _PIL_AVAILABLE = True
except ImportError:
    _PIL_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    _PYMUPDF_AVAILABLE = True
except ImportError:
    _PYMUPDF_AVAILABLE = False


@dataclass
class OcrResult:
    """OCR result container"""
    text: str
    confidence: float = 0.0
    success: bool = True
    error: Optional[str] = None


class PaddleOcrService:
    """
    PaddleOCR-based OCR service with English and Chinese support
    Provides a clean interface for image and PDF text extraction
    """
    
    def __init__(self):
        self._ocr = None
        self._init_error: Optional[str] = None
        self._status = "initializing"
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """Initialize PaddleOCR with error handling"""
        try:
            # Import PaddleOCR
            from paddleocr import PaddleOCR

            # Check if we can import successfully
            self._paddle_ocr_class = PaddleOCR
            self._ocr = None  # Will be initialized on first use
            self._status = "available:paddleocr"

        except ImportError as e:
            self._init_error = f"PaddleOCR not installed: {str(e)}"
            self._status = "unavailable:missing_paddleocr"

        except Exception as e:
            self._init_error = f"PaddleOCR import failed: {str(e)}"
            self._status = "unavailable:import_failed"

    def _ensure_ocr_initialized(self):
        """Ensure OCR is initialized (lazy initialization)"""
        if self._ocr is None and hasattr(self, '_paddle_ocr_class'):
            try:
                # Initialize PaddleOCR with PP-OCRv5 models for best performance
                # Using Chinese+English support for maximum compatibility
                # Simplified initialization to avoid parameter conflicts
                self._ocr = self._paddle_ocr_class(lang='ch')
                print("✅ PaddleOCR initialized successfully with PP-OCRv5 models")
            except Exception as e:
                # If initialization fails, try fallback configurations
                error_msg = str(e)
                if "already been initialized" in error_msg or "Unknown argument" in error_msg:
                    # Try simpler initialization
                    try:
                        self._ocr = self._paddle_ocr_class(lang='ch')
                        print("✅ PaddleOCR initialized with fallback configuration")
                    except Exception as fallback_error:
                        self._init_error = f"PaddleOCR initialization failed: {fallback_error}"
                        self._status = "unavailable:init_failed"
                        raise RuntimeError(self._init_error)
                else:
                    self._init_error = f"PaddleOCR initialization failed: {error_msg}"
                    self._status = "unavailable:init_failed"
                    raise RuntimeError(self._init_error)
    
    @property
    def status(self) -> str:
        """Get current OCR service status"""
        return self._status
    
    def is_available(self) -> bool:
        """Check if OCR service is available"""
        return (self._ocr is not None or hasattr(self, '_paddle_ocr_class')) and _PIL_AVAILABLE
    
    def _normalize_paddle_result(self, result) -> OcrResult:
        """
        Normalize PaddleOCR result format
        Handles both legacy and modern PaddleOCR v3.2.0+ formats
        """
        if not result:
            return OcrResult(text="", confidence=0.0)

        texts = []
        confidences = []

        try:
            # Handle modern PaddleOCR v3.2.0+ format (dictionary with rec_texts and rec_scores)
            if isinstance(result, list) and len(result) > 0:
                first_item = result[0]

                # Modern format: [{'rec_texts': [...], 'rec_scores': [...], ...}]
                if isinstance(first_item, dict) and 'rec_texts' in first_item and 'rec_scores' in first_item:
                    rec_texts = first_item['rec_texts']
                    rec_scores = first_item['rec_scores']

                    for i, text in enumerate(rec_texts):
                        if text and text.strip():
                            texts.append(text.strip())
                            if i < len(rec_scores):
                                confidences.append(float(rec_scores[i]))
                            else:
                                confidences.append(0.0)

                # Legacy format: [[[bbox], (text, confidence)], ...]
                elif isinstance(first_item, list):
                    # If result is nested (common in older versions)
                    if len(first_item) > 0 and isinstance(first_item[0], list):
                        result = first_item  # Unwrap one level

                    # Process each detected text line
                    for line in result:
                        if isinstance(line, list) and len(line) >= 2:
                            # Extract text and confidence: [bbox, (text, confidence)]
                            text_info = line[1]
                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                text, confidence = text_info[0], text_info[1]
                                if text and text.strip():
                                    texts.append(text.strip())
                                    confidences.append(float(confidence))
                        elif isinstance(line, dict):
                            # Handle dictionary format (alternative format)
                            if 'text' in line and 'confidence' in line:
                                text = line['text'].strip()
                                if text:
                                    texts.append(text)
                                    confidences.append(float(line['confidence']))

            # Combine all text with proper spacing
            combined_text = " ".join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0

            return OcrResult(
                text=combined_text,
                confidence=avg_confidence,
                success=True
            )

        except Exception as e:
            return OcrResult(
                text="",
                confidence=0.0,
                success=False,
                error=f"Result parsing error: {str(e)}"
            )
    
    def _preprocess_image(self, image: Image.Image, max_side_limit: int = 4000) -> Image.Image:
        """
        Preprocess image to ensure it meets size requirements and is optimized for OCR

        Args:
            image: PIL Image object
            max_side_limit: Maximum allowed dimension for either width or height

        Returns:
            Preprocessed PIL Image object
        """
        width, height = image.size

        # Check for extremely narrow images that cause OCR pipeline failures
        min_dimension = 32  # Minimum dimension for OCR models
        aspect_ratio = max(width, height) / min(width, height) if min(width, height) > 0 else float('inf')
        max_aspect_ratio = 20  # Maximum allowed aspect ratio

        # Handle extremely narrow images by padding them to a more reasonable aspect ratio
        if aspect_ratio > max_aspect_ratio or min(width, height) < min_dimension:
            print(f"Image dimensions ({width}x{height}) are problematic for OCR (aspect ratio: {aspect_ratio:.1f})")

            # Calculate target dimensions that maintain content but improve aspect ratio
            if width < min_dimension or height < min_dimension:
                # Scale up tiny images while maintaining aspect ratio
                scale_factor = max(min_dimension / width, min_dimension / height)
                new_width = max(int(width * scale_factor), min_dimension)
                new_height = max(int(height * scale_factor), min_dimension)
                print(f"Scaling up tiny image to {new_width}x{new_height}")
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                width, height = new_width, new_height

            # If still too narrow, pad the shorter dimension
            if max(width, height) / min(width, height) > max_aspect_ratio:
                if width < height:
                    # Image is too tall and narrow, pad width
                    target_width = max(min_dimension, height // max_aspect_ratio)
                    padding = (target_width - width) // 2
                    print(f"Padding narrow image: adding {padding*2} pixels to width")
                    # Create new image with padding
                    new_image = Image.new('RGB', (target_width, height), color='white')
                    new_image.paste(image, (padding, 0))
                    image = new_image
                else:
                    # Image is too wide and short, pad height
                    target_height = max(min_dimension, width // max_aspect_ratio)
                    padding = (target_height - height) // 2
                    print(f"Padding short image: adding {padding*2} pixels to height")
                    # Create new image with padding
                    new_image = Image.new('RGB', (width, target_height), color='white')
                    new_image.paste(image, (0, padding))
                    image = new_image

                width, height = image.size
                print(f"After padding: {width}x{height} (aspect ratio: {max(width, height) / min(width, height):.1f})")

        # Check if image exceeds the maximum side limit
        if max(width, height) > max_side_limit:
            print(f"Image size ({width}x{height}) exceeds max_side_limit of {max_side_limit}. Preprocessing to fit within limit.")

            # Calculate the scaling ratio to fit within the limit while preserving aspect ratio
            ratio = float(max_side_limit) / max(width, height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)

            # Ensure dimensions are at least minimum pixels
            new_width = max(new_width, min_dimension)
            new_height = max(new_height, min_dimension)

            print(f"Resizing image from {width}x{height} to {new_width}x{new_height} (ratio: {ratio:.3f})")

            # Use high-quality resampling for better OCR results
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        return image

    def _validate_image_for_ocr(self, image: Image.Image) -> tuple[bool, str]:
        """
        Validate if an image is suitable for OCR processing

        Args:
            image: PIL Image object

        Returns:
            Tuple of (is_valid, reason_if_invalid)
        """
        width, height = image.size

        # Check minimum dimensions
        if width < 1 or height < 1:
            return False, f"Invalid dimensions: {width}x{height}"

        # Check for extremely small images that won't contain readable text
        min_area = 100  # Minimum area in pixels
        if width * height < min_area:
            return False, f"Image too small for OCR: {width}x{height} (area: {width*height} < {min_area})"

        # Check aspect ratio - extremely narrow images cause pipeline failures
        aspect_ratio = max(width, height) / min(width, height)
        max_aspect_ratio = 50  # Very generous limit
        if aspect_ratio > max_aspect_ratio:
            return False, f"Extreme aspect ratio: {aspect_ratio:.1f} (limit: {max_aspect_ratio})"

        # Check for reasonable minimum dimensions after our preprocessing
        min_dimension = 10  # Very permissive minimum
        if min(width, height) < min_dimension:
            return False, f"Dimension too small: min({width}, {height}) < {min_dimension}"

        return True, ""

    def extract_text_from_image(self, image_bytes: bytes) -> str:
        """
        Extract text from image bytes with robust preprocessing

        Args:
            image_bytes: Raw image data

        Returns:
            Extracted text string

        Raises:
            RuntimeError: If OCR service is not available or processing fails
        """
        if not self.is_available():
            raise RuntimeError(f"OCR service not available: {self._init_error or 'Unknown error'}")

        if not _PIL_AVAILABLE:
            raise RuntimeError("PIL (Pillow) is required for image processing")

        try:
            # Ensure OCR is initialized
            self._ensure_ocr_initialized()

            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # Log original image dimensions
            original_width, original_height = image.size
            print(f"Processing image: {original_width}x{original_height} pixels")

            # Validate image before processing
            is_valid, validation_error = self._validate_image_for_ocr(image)
            if not is_valid:
                print(f"Image validation failed: {validation_error}")
                print("Attempting to preprocess image to make it suitable for OCR...")
                # Continue with preprocessing - it might fix the issues

            # Convert to RGB if necessary (PaddleOCR works best with RGB)
            if image.mode != 'RGB':
                original_mode = image.mode
                image = image.convert('RGB')
                print(f"Converted image from {original_mode} to RGB mode")

            # Preprocess image to handle size constraints and problematic dimensions
            image = self._preprocess_image(image, max_side_limit=4000)

            # Validate again after preprocessing
            is_valid_after, validation_error_after = self._validate_image_for_ocr(image)
            if not is_valid_after:
                print(f"Image still invalid after preprocessing: {validation_error_after}")
                print("Returning empty result to avoid OCR pipeline failure")
                return ""

            # Convert PIL Image to numpy array (required by PaddleOCR v3.2.0+)
            image_array = np.array(image)

            print(f"Image array shape: {image_array.shape}")

            # Perform OCR using the modern predict API
            # The predict method is the recommended approach in PaddleOCR v3.2.0+
            try:
                print("Running OCR using predict API...")
                result = self._ocr.predict(image_array)
            except AttributeError:
                # Fallback to ocr method if predict is not available
                print("Predict API not available, falling back to legacy OCR method...")
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", DeprecationWarning)
                    result = self._ocr.ocr(image_array)
            except Exception as api_error:
                # If predict fails, try the legacy ocr method
                print(f"Predict API failed ({api_error}), trying legacy OCR method...")
                try:
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore", DeprecationWarning)
                        result = self._ocr.ocr(image_array)
                except Exception as legacy_error:
                    raise RuntimeError(f"Both OCR APIs failed - Predict: {api_error}, Legacy: {legacy_error}")

            # Normalize result
            ocr_result = self._normalize_paddle_result(result)

            if not ocr_result.success:
                raise RuntimeError(ocr_result.error or "OCR processing failed")

            print(f"OCR completed successfully. Extracted text length: {len(ocr_result.text)}")
            return ocr_result.text

        except Exception as e:
            error_msg = str(e)
            print(f"OCR processing error: {error_msg}")

            # Provide more informative error messages
            if "unexpected keyword argument" in error_msg:
                raise RuntimeError(f"PaddleOCR API compatibility issue: {error_msg}")
            elif "not supported input data type" in error_msg:
                raise RuntimeError(f"Image format not supported by PaddleOCR: {error_msg}")
            elif "max_side_limit" in error_msg.lower():
                raise RuntimeError(f"Image size constraint error: {error_msg}")
            else:
                raise RuntimeError(f"Image OCR failed: {error_msg}")
    
    def extract_text_from_pdf(self, pdf_bytes: bytes) -> str:
        """
        Extract text from PDF bytes using OCR
        
        Args:
            pdf_bytes: Raw PDF data
            
        Returns:
            Extracted text string
            
        Raises:
            RuntimeError: If OCR service is not available or processing fails
        """
        if not self.is_available():
            raise RuntimeError(f"OCR service not available: {self._init_error or 'Unknown error'}")
        
        if not _PYMUPDF_AVAILABLE:
            raise RuntimeError("PyMuPDF is required for PDF processing")
        
        doc = None
        texts = []
        
        try:
            # Open PDF document
            doc = fitz.open(stream=pdf_bytes, filetype='pdf')

            for page_num in range(len(doc)):
                try:
                    page = doc.load_page(page_num)

                    # First try to extract native text
                    try:
                        native_text = page.get_text().strip()
                        if native_text:
                            texts.append(native_text)
                            continue
                    except Exception as e:
                        print(f"Text extraction failed for page {page_num + 1}: {e}")
                        # Continue to try OCR even if text extraction fails

                    # If no native text or text extraction failed, use OCR
                    # Convert page to image with error handling for MuPDF issues
                    try:
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x scale for better OCR
                        img_data = pix.tobytes("png")

                        # Extract text using OCR
                        try:
                            ocr_text = self.extract_text_from_image(img_data)
                            if ocr_text:
                                texts.append(ocr_text)
                        except Exception as e:
                            # Log OCR failure but continue with other pages
                            print(f"OCR failed for page {page_num + 1}: {e}")
                            continue

                    except Exception as e:
                        # Handle MuPDF rendering errors (like ExtGState resource errors)
                        error_msg = str(e).lower()
                        if "extgstate" in error_msg:
                            print(f"MuPDF ExtGState error on page {page_num + 1}: {e}")
                            print(f"  This is typically caused by corrupted or malformed PDF graphics")
                            print(f"  Skipping OCR for this page, but continuing with document processing")
                        elif "syntax error" in error_msg:
                            print(f"MuPDF syntax error on page {page_num + 1}: {e}")
                            print(f"  This indicates PDF structure issues")
                            print(f"  Skipping OCR for this page, but continuing with document processing")
                        else:
                            print(f"MuPDF rendering error on page {page_num + 1}: {e}")
                            print(f"  Skipping OCR for this page, but continuing with document processing")
                        continue

                except Exception as e:
                    # Handle page loading errors
                    print(f"Failed to load page {page_num + 1}: {e}")
                    continue

            return "\n\n".join(texts)
            
        except Exception as e:
            raise RuntimeError(f"PDF OCR failed: {str(e)}")
            
        finally:
            if doc:
                doc.close()
    
    def get_info(self) -> dict:
        """Get OCR service information"""
        return {
            'service': 'PaddleOCR v3.2.0',
            'model': 'PP-OCRv5 (Latest)',
            'status': self.status,
            'available': self.is_available(),
            'languages': ['English', 'Chinese'],
            'features': [
                'PP-OCRv5 Text Detection',
                'PP-OCRv5 Text Recognition',
                'Document Orientation Classification',
                'Multi-language Support',
                'CPU Optimized Processing'
            ],
            'processing': 'CPU-only (GPU not required)',
            'error': self._init_error
        }


# Backward compatibility alias
OcrService = PaddleOcrService
